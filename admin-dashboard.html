<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }
        
        .admin-layout {
            display: flex;
            height: calc(100vh - 44px);
        }
        
        .sidebar {
            width: 80px;
            background-color: #001529;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 0;
            overflow-y: auto;
        }
        
        .sidebar-logo {
            width: 40px;
            height: 40px;
            background-color: #1890ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            color: white;
            font-size: 20px;
        }
        
        .sidebar-menu {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        
        .menu-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            color: rgba(255, 255, 255, 0.65);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: white;
        }
        
        .menu-item.active {
            color: white;
            background-color: #1890ff;
        }
        
        .menu-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-left: 16px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .header-icon:hover {
            color: #1890ff;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 16px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .trend-up {
            color: #52c41a;
        }
        
        .trend-down {
            color: #ff4d4f;
        }
        
        .trend-icon {
            margin-right: 4px;
        }
        
        .chart-cards {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .chart-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .chart-actions {
            display: flex;
            align-items: center;
        }
        
        .chart-action {
            color: #666;
            font-size: 14px;
            margin-left: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .chart-action:hover {
            color: #1890ff;
        }
        
        .chart-content {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #bfbfbf;
        }
        
        .table-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .table-actions {
            display: flex;
            align-items: center;
        }
        
        .table-action {
            color: #666;
            font-size: 14px;
            margin-left: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .table-action:hover {
            color: #1890ff;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-store"></i>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item active">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user-check menu-icon"></i>
                    <span>审核</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span>统计</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>设置</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <i class="fas fa-bars" style="font-size: 18px; color: #666; cursor: pointer;"></i>
                    <div class="header-title">仪表盘</div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">5</div>
                    </div>
                    <div class="user-avatar">
                        <span>管</span>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-title">总商家数</div>
                        <div class="stat-value">1,286</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>16.8% 较上月</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">待审核商家</div>
                        <div class="stat-value">42</div>
                        <div class="stat-trend trend-down">
                            <i class="fas fa-arrow-down trend-icon"></i>
                            <span>8.2% 较上月</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">今日订单数</div>
                        <div class="stat-value">3,754</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>12.5% 较昨日</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">今日交易额</div>
                        <div class="stat-value">¥89,432</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>9.3% 较昨日</span>
                        </div>
                    </div>
                </div>
                
                <div class="chart-cards">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">商家入驻趋势</div>
                            <div class="chart-actions">
                                <span class="chart-action">本周</span>
                                <span class="chart-action">本月</span>
                                <span class="chart-action">全年</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-line" style="font-size: 80px;"></i>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">商家类型分布</div>
                            <div class="chart-actions">
                                <i class="fas fa-ellipsis-v chart-action"></i>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-pie" style="font-size: 80px;"></i>
                        </div>
                    </div>
                </div>
                
                <div class="table-card">
                    <div class="table-header">
                        <div class="table-title">最近入驻商家</div>
                        <div class="table-actions">
                            <span class="table-action">查看全部</span>
                        </div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>商家名称</th>
                                <th>行业类型</th>
                                <th>申请时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>品味小厨</td>
                                <td>餐饮</td>
                                <td>2023-05-18 14:23</td>
                                <td><span class="status-tag status-pending">待审核</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                            <tr>
                                <td>鲜花坊</td>
                                <td>零售</td>
                                <td>2023-05-18 11:05</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                            <tr>
                                <td>美丽发廊</td>
                                <td>美容美发</td>
                                <td>2023-05-17 16:42</td>
                                <td><span class="status-tag status-rejected">已拒绝</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                            <tr>
                                <td>健身工作室</td>
                                <td>健身</td>
                                <td>2023-05-17 09:18</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                            <tr>
                                <td>快乐童装</td>
                                <td>零售</td>
                                <td>2023-05-16 15:37</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
