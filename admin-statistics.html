<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计报表</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }
        
        .admin-layout {
            display: flex;
            height: calc(100vh - 44px);
        }
        
        .sidebar {
            width: 80px;
            background-color: #001529;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 0;
            overflow-y: auto;
        }
        
        .sidebar-logo {
            width: 40px;
            height: 40px;
            background-color: #1890ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            color: white;
            font-size: 20px;
        }
        
        .sidebar-menu {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        
        .menu-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            color: rgba(255, 255, 255, 0.65);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: white;
        }
        
        .menu-item.active {
            color: white;
            background-color: #1890ff;
        }
        
        .menu-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-left: 16px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .header-icon:hover {
            color: #1890ff;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 16px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .page-actions {
            display: flex;
            align-items: center;
        }
        
        .action-button {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin-left: 8px;
        }
        
        .action-button:hover {
            background-color: #40a9ff;
        }
        
        .action-button i {
            margin-right: 8px;
        }
        
        .filter-bar {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 24px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .filter-label {
            font-size: 14px;
            color: #666;
            margin-right: 8px;
        }
        
        .filter-select {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            margin-right: 16px;
            min-width: 120px;
        }
        
        .date-range {
            display: flex;
            align-items: center;
            margin-left: auto;
        }
        
        .date-input {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 130px;
        }
        
        .date-separator {
            margin: 0 8px;
            color: #999;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .trend-up {
            color: #52c41a;
        }
        
        .trend-down {
            color: #ff4d4f;
        }
        
        .trend-icon {
            margin-right: 4px;
        }
        
        .chart-cards {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .chart-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .chart-actions {
            display: flex;
            align-items: center;
        }
        
        .chart-action {
            color: #666;
            font-size: 14px;
            margin-left: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .chart-action:hover {
            color: #1890ff;
        }
        
        .chart-content {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #bfbfbf;
        }
        
        .ranking-list {
            margin-top: 16px;
        }
        
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .ranking-item:last-child {
            border-bottom: none;
        }
        
        .ranking-number {
            width: 20px;
            height: 20px;
            background-color: #f5f5f5;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            margin-right: 12px;
        }
        
        .ranking-number.top-1 {
            background-color: #ff4d4f;
            color: white;
        }
        
        .ranking-number.top-2 {
            background-color: #fa8c16;
            color: white;
        }
        
        .ranking-number.top-3 {
            background-color: #faad14;
            color: white;
        }
        
        .ranking-info {
            flex: 1;
        }
        
        .ranking-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }
        
        .ranking-meta {
            font-size: 12px;
            color: #999;
        }
        
        .ranking-value {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .table-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            margin-bottom: 24px;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .table-actions {
            display: flex;
            align-items: center;
        }
        
        .table-action {
            color: #666;
            font-size: 14px;
            margin-left: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .table-action:hover {
            color: #1890ff;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-store"></i>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user-check menu-icon"></i>
                    <span>审核</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span>统计</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>设置</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <i class="fas fa-bars" style="font-size: 18px; color: #666; cursor: pointer;"></i>
                    <div class="header-title">统计报表</div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">5</div>
                    </div>
                    <div class="user-avatar">
                        <span>管</span>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <div class="page-title">数据统计</div>
                    <div class="page-actions">
                        <button class="action-button">
                            <i class="fas fa-download"></i>
                            <span>导出报表</span>
                        </button>
                        <button class="action-button">
                            <i class="fas fa-print"></i>
                            <span>打印报表</span>
                        </button>
                    </div>
                </div>
                
                <div class="filter-bar">
                    <span class="filter-label">数据类型:</span>
                    <select class="filter-select">
                        <option value="1">商家数据</option>
                        <option value="2">订单数据</option>
                        <option value="3">交易数据</option>
                        <option value="4">用户数据</option>
                    </select>
                    
                    <span class="filter-label">时间维度:</span>
                    <select class="filter-select">
                        <option value="1">按日</option>
                        <option value="2">按周</option>
                        <option value="3">按月</option>
                        <option value="4">按季度</option>
                        <option value="5">按年</option>
                    </select>
                    
                    <div class="date-range">
                        <input type="date" class="date-input" value="2023-05-01">
                        <span class="date-separator">至</span>
                        <input type="date" class="date-input" value="2023-05-18">
                    </div>
                </div>
                
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-title">总商家数</div>
                        <div class="stat-value">1,286</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>16.8% 较上月</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">活跃商家</div>
                        <div class="stat-value">876</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>12.3% 较上月</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">总订单数</div>
                        <div class="stat-value">89,754</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>8.5% 较上月</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">总交易额</div>
                        <div class="stat-value">¥1,892,432</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>9.7% 较上月</span>
                        </div>
                    </div>
                </div>
                
                <div class="chart-cards">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">商家入驻趋势</div>
                            <div class="chart-actions">
                                <span class="chart-action">本周</span>
                                <span class="chart-action">本月</span>
                                <span class="chart-action">全年</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-line" style="font-size: 80px;"></i>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">热门行业排行</div>
                            <div class="chart-actions">
                                <i class="fas fa-ellipsis-v chart-action"></i>
                            </div>
                        </div>
                        <div class="ranking-list">
                            <div class="ranking-item">
                                <div class="ranking-number top-1">1</div>
                                <div class="ranking-info">
                                    <div class="ranking-name">餐饮</div>
                                    <div class="ranking-meta">428家商户</div>
                                </div>
                                <div class="ranking-value">33.3%</div>
                            </div>
                            <div class="ranking-item">
                                <div class="ranking-number top-2">2</div>
                                <div class="ranking-info">
                                    <div class="ranking-name">零售</div>
                                    <div class="ranking-meta">356家商户</div>
                                </div>
                                <div class="ranking-value">27.7%</div>
                            </div>
                            <div class="ranking-item">
                                <div class="ranking-number top-3">3</div>
                                <div class="ranking-info">
                                    <div class="ranking-name">美容美发</div>
                                    <div class="ranking-meta">187家商户</div>
                                </div>
                                <div class="ranking-value">14.5%</div>
                            </div>
                            <div class="ranking-item">
                                <div class="ranking-number">4</div>
                                <div class="ranking-info">
                                    <div class="ranking-name">健身</div>
                                    <div class="ranking-meta">124家商户</div>
                                </div>
                                <div class="ranking-value">9.6%</div>
                            </div>
                            <div class="ranking-item">
                                <div class="ranking-number">5</div>
                                <div class="ranking-info">
                                    <div class="ranking-name">教育</div>
                                    <div class="ranking-meta">98家商户</div>
                                </div>
                                <div class="ranking-value">7.6%</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="chart-cards">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">订单数量趋势</div>
                            <div class="chart-actions">
                                <span class="chart-action">本周</span>
                                <span class="chart-action">本月</span>
                                <span class="chart-action">全年</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-area" style="font-size: 80px;"></i>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">交易额分布</div>
                            <div class="chart-actions">
                                <i class="fas fa-ellipsis-v chart-action"></i>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-pie" style="font-size: 80px;"></i>
                        </div>
                    </div>
                </div>
                
                <div class="table-card">
                    <div class="table-header">
                        <div class="table-title">商家业绩排行</div>
                        <div class="table-actions">
                            <span class="table-action">查看全部</span>
                        </div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>商家名称</th>
                                <th>行业</th>
                                <th>订单数</th>
                                <th>交易额</th>
                                <th>同比增长</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>品味小厨</td>
                                <td>餐饮</td>
                                <td>3,245</td>
                                <td>¥128,760</td>
                                <td><span class="trend-up">+24.5%</span></td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>鲜花坊</td>
                                <td>零售</td>
                                <td>2,876</td>
                                <td>¥98,432</td>
                                <td><span class="trend-up">+18.2%</span></td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>美丽发廊</td>
                                <td>美容美发</td>
                                <td>1,987</td>
                                <td>¥87,654</td>
                                <td><span class="trend-up">+15.7%</span></td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>健身工作室</td>
                                <td>健身</td>
                                <td>1,654</td>
                                <td>¥76,543</td>
                                <td><span class="trend-down">-3.2%</span></td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>快乐童装</td>
                                <td>零售</td>
                                <td>1,432</td>
                                <td>¥65,432</td>
                                <td><span class="trend-up">+8.9%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
