<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运营配置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .config-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }
        .config-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        .config-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f1f5f9;
        }
        .config-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: white;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.2s;
            background: #fafbfc;
        }
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
            background: white;
        }
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        .btn {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #3b82f6;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .distribution-item {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.2s;
        }
        .distribution-item:hover {
            border-color: #3b82f6;
            background: #f1f5f9;
        }
        .rider-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.2s;
        }
        .rider-card:hover {
            border-color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-pending { background: #fef3c7; color: #d97706; }
        .status-approved { background: #d1fae5; color: #059669; }
        .status-rejected { background: #fee2e2; color: #dc2626; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 校园外卖配置 -->
        <div class="config-card">
            <div class="config-header">
                <div class="config-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                    <i class="fas fa-university"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900">校园外卖配置</h3>
                    <p class="text-gray-500">设置校园外卖的基础参数和规则</p>
                </div>
            </div>
            
            <div class="grid grid-cols-2 gap-6">
                <div class="form-group">
                    <label class="form-label">配送范围 (公里)</label>
                    <input type="number" class="form-input" value="5" step="0.1">
                </div>
                <div class="form-group">
                    <label class="form-label">起送金额 (元)</label>
                    <input type="number" class="form-input" value="20" step="0.1">
                </div>
                <div class="form-group">
                    <label class="form-label">配送费 (元)</label>
                    <input type="number" class="form-input" value="3" step="0.1">
                </div>
                <div class="form-group">
                    <label class="form-label">平台抽成比例 (%)</label>
                    <input type="number" class="form-input" value="15" step="0.1">
                </div>
                <div class="form-group">
                    <label class="form-label">营业时间 - 开始</label>
                    <input type="time" class="form-input" value="07:00">
                </div>
                <div class="form-group">
                    <label class="form-label">营业时间 - 结束</label>
                    <input type="time" class="form-input" value="22:00">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">配送说明</label>
                <textarea class="form-textarea" placeholder="请输入配送相关说明...">校园内配送，预计30-45分钟送达。请确保手机畅通，配送员会提前联系。</textarea>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="form-label mr-4">启用校园外卖服务</span>
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                <button class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>保存配置
                </button>
            </div>
        </div>

        <!-- 分销信息录入 -->
        <div class="config-card">
            <div class="config-header">
                <div class="config-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-network-wired"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900">分销信息管理</h3>
                    <p class="text-gray-500">管理分销公司信息和分成设置</p>
                </div>
            </div>
            
            <div class="mb-6">
                <button class="btn btn-success">
                    <i class="fas fa-plus mr-2"></i>添加分销公司
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="distribution-item">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">校园配送有限公司</h4>
                            <p class="text-sm text-gray-500">负责人: 张经理 | 联系电话: 138****5678</p>
                        </div>
                        <div class="flex gap-2">
                            <button class="btn btn-warning text-xs">编辑</button>
                            <button class="btn btn-danger text-xs">删除</button>
                        </div>
                    </div>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="form-group">
                            <label class="form-label">分成比例 (%)</label>
                            <input type="number" class="form-input" value="15" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">服务区域</label>
                            <select class="form-select">
                                <option>东区宿舍楼</option>
                                <option>西区宿舍楼</option>
                                <option>南区宿舍楼</option>
                                <option>北区宿舍楼</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select class="form-select">
                                <option>正常运营</option>
                                <option>暂停服务</option>
                                <option>维护中</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="distribution-item">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">快递小哥配送</h4>
                            <p class="text-sm text-gray-500">负责人: 李主管 | 联系电话: 139****1234</p>
                        </div>
                        <div class="flex gap-2">
                            <button class="btn btn-warning text-xs">编辑</button>
                            <button class="btn btn-danger text-xs">删除</button>
                        </div>
                    </div>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="form-group">
                            <label class="form-label">分成比例 (%)</label>
                            <input type="number" class="form-input" value="12" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">服务区域</label>
                            <select class="form-select">
                                <option>全校区</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select class="form-select">
                                <option>正常运营</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 text-right">
                <button class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>保存分销设置
                </button>
            </div>
        </div>

        <!-- 骑手审核 -->
        <div class="config-card">
            <div class="config-header">
                <div class="config-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-motorcycle"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900">骑手审核管理</h3>
                    <p class="text-gray-500">审核和管理配送骑手信息</p>
                </div>
            </div>
            
            <div class="mb-6 flex items-center justify-between">
                <div class="flex gap-4">
                    <select class="form-select w-48">
                        <option>全部状态</option>
                        <option>待审核</option>
                        <option>已通过</option>
                        <option>已拒绝</option>
                    </select>
                    <select class="form-select w-48">
                        <option>全部分销公司</option>
                        <option>校园配送有限公司</option>
                        <option>快递小哥配送</option>
                    </select>
                </div>
                <div class="text-sm text-gray-500">
                    待审核: <span class="font-semibold text-orange-600">8</span> 人
                </div>
            </div>
            
            <div class="space-y-4">
                <div class="rider-card">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">王师傅</h4>
                                <p class="text-sm text-gray-500">手机: 138****5678 | 身份证: 320***********1234</p>
                                <p class="text-sm text-gray-500">所属: 校园配送有限公司 | 申请时间: 2024-02-20</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4">
                            <span class="status-badge status-pending">待审核</span>
                            <div class="flex gap-2">
                                <button class="btn btn-success text-xs">通过</button>
                                <button class="btn btn-danger text-xs">拒绝</button>
                                <button class="btn btn-primary text-xs">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="rider-card">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-green-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">李师傅</h4>
                                <p class="text-sm text-gray-500">手机: 139****1234 | 身份证: 320***********5678</p>
                                <p class="text-sm text-gray-500">所属: 快递小哥配送 | 审核时间: 2024-02-19</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4">
                            <span class="status-badge status-approved">已通过</span>
                            <div class="flex gap-2">
                                <button class="btn btn-warning text-xs">暂停</button>
                                <button class="btn btn-primary text-xs">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="rider-card">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-red-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">赵师傅</h4>
                                <p class="text-sm text-gray-500">手机: 137****9876 | 身份证: 320***********9012</p>
                                <p class="text-sm text-gray-500">所属: 校园配送有限公司 | 审核时间: 2024-02-18</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4">
                            <span class="status-badge status-rejected">已拒绝</span>
                            <div class="flex gap-2">
                                <button class="btn btn-primary text-xs">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    显示 1-10 条，共 25 条记录
                </div>
                <div class="flex gap-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-blue-500 text-white rounded-md text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
