<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合作商管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            width: 1440px;
            margin: 0;
            padding: 0;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-pending { background: #fef3c7; color: #d97706; }
        .status-approved { background: #d1fae5; color: #059669; }
        .status-rejected { background: #fee2e2; color: #dc2626; }
        .status-active { background: #dbeafe; color: #2563eb; }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .action-btn {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .search-box {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            transition: all 0.2s;
        }
        .search-box:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 搜索和筛选区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="flex flex-wrap gap-4 items-center justify-between">
                <div class="flex gap-4 items-center flex-1">
                    <div class="relative flex-1 max-w-md">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input type="text" placeholder="搜索商家名称、联系人..." 
                               class="search-box w-full pl-12 outline-none">
                    </div>
                    <select class="search-box outline-none">
                        <option>全部状态</option>
                        <option>待审核</option>
                        <option>已通过</option>
                        <option>已拒绝</option>
                        <option>运营中</option>
                    </select>
                    <select class="search-box outline-none">
                        <option>全部类型</option>
                        <option>餐饮</option>
                        <option>超市</option>
                        <option>水果</option>
                        <option>其他</option>
                    </select>
                </div>
                <button class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                    <i class="fas fa-plus mr-2"></i>添加商家
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">总商家数</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-lg">
                        <i class="fas fa-store text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">待审核</p>
                        <p class="text-2xl font-bold text-orange-600">23</p>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-lg">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">运营中</p>
                        <p class="text-2xl font-bold text-green-600">128</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-lg">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">本月新增</p>
                        <p class="text-2xl font-bold text-purple-600">12</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-lg">
                        <i class="fas fa-plus-circle text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商家列表 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">商家列表</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商家信息</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经营类型</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分销信息</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                                        <i class="fas fa-utensils text-orange-600"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">麻辣香锅店</div>
                                        <div class="text-sm text-gray-500">ID: MC001</div>
                                        <div class="text-sm text-gray-500">注册时间: 2024-01-15</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">张老板</div>
                                <div class="text-sm text-gray-500">13812345678</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    餐饮
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-badge status-active">运营中</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">分销公司: 校园配送</div>
                                <div class="text-sm text-gray-500">分成比例: 15%</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex gap-2">
                                    <button class="action-btn btn-primary">查看</button>
                                    <button class="action-btn btn-warning">编辑</button>
                                    <button class="action-btn btn-danger">暂停</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                        <i class="fas fa-apple-alt text-green-600"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">新鲜水果店</div>
                                        <div class="text-sm text-gray-500">ID: MC002</div>
                                        <div class="text-sm text-gray-500">注册时间: 2024-01-20</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">李经理</div>
                                <div class="text-sm text-gray-500">13987654321</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    水果
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-badge status-pending">待审核</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-500">待分配</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex gap-2">
                                    <button class="action-btn btn-success">审核</button>
                                    <button class="action-btn btn-primary">查看</button>
                                    <button class="action-btn btn-danger">拒绝</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                        <i class="fas fa-shopping-basket text-purple-600"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">便民超市</div>
                                        <div class="text-sm text-gray-500">ID: MC003</div>
                                        <div class="text-sm text-gray-500">注册时间: 2024-02-01</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">王店长</div>
                                <div class="text-sm text-gray-500">13765432109</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    超市
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-badge status-approved">已通过</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">分销公司: 快递小哥</div>
                                <div class="text-sm text-gray-500">分成比例: 12%</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex gap-2">
                                    <button class="action-btn btn-primary">查看</button>
                                    <button class="action-btn btn-success">启用</button>
                                    <button class="action-btn btn-warning">编辑</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    显示 1-10 条，共 156 条记录
                </div>
                <div class="flex gap-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-blue-500 text-white rounded-md text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
