<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序原型</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f6f6f6;
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }
        
        /* iOS Status Bar */
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }
        
        .status-icons {
            display: flex;
            align-items: center;
        }
        
        .status-icons i {
            margin-left: 5px;
        }
        
        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }
        
        .wechat-nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
        }
        
        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px);
            overflow-y: auto;
            padding: 16px;
            background-color: #f6f6f6;
        }
        
        /* Bottom Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }
        
        .tab-item.active {
            color: #07C160;
        }
        
        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }
        
        /* Common Components */
        .card {
            background-color: #fff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 10px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
            margin-top: 16px;
        }
        
        .btn-secondary {
            background-color: #F2F2F2;
            color: #333;
            border-radius: 4px;
            padding: 10px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
            margin-top: 16px;
        }
        
        .input-field {
            border: none;
            border-bottom: 1px solid #EBEBEB;
            padding: 12px 0;
            width: 100%;
            font-size: 16px;
            margin-bottom: 16px;
        }
        
        .section-title {
            font-size: 14px;
            color: #999;
            margin-bottom: 8px;
        }
        
        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #EBEBEB;
        }
        
        .list-item:last-child {
            border-bottom: none;
        }
        
        .list-item-title {
            font-size: 16px;
            color: #333;
        }
        
        .list-item-subtitle {
            font-size: 14px;
            color: #999;
            margin-top: 4px;
        }
        
        .list-item-right {
            color: #999;
            display: flex;
            align-items: center;
        }
        
        .list-item-right i {
            margin-left: 4px;
        }
        
        .upload-area {
            border: 1px dashed #DEDEDE;
            border-radius: 4px;
            padding: 24px;
            text-align: center;
            color: #999;
            margin-bottom: 16px;
        }
        
        .upload-area i {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .tag {
            display: inline-block;
            background-color: #F2F2F2;
            color: #666;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .tag.active {
            background-color: #E8F5E9;
            color: #07C160;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="wechat-nav-title">页面标题</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>
    
    <!-- Content Area -->
    <div class="content-area">
        <!-- Content goes here -->
    </div>
    
    <!-- Bottom Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
