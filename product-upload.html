<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品上传</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
        }
        .status-bar::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 24px;
            background-color: #000;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .progress-bar {
            height: 4px;
            background-color: #e5e5e5;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #07c160;
            width: 50%;
        }
        .product-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            margin-bottom: 16px;
        }
        .product-image {
            height: 130px;
            background-size: cover;
            background-position: center;
            position: relative;
        }
        .product-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 8px;
        }
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            color: #333;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
        }
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        .category-tabs {
            display: flex;
            overflow-x: auto;
            gap: 8px;
            padding: 0 4px;
            margin-bottom: 12px;
        }
        .category-tab {
            padding: 6px 12px;
            background-color: #f3f4f6;
            border-radius: 16px;
            font-size: 12px;
            white-space: nowrap;
        }
        .category-tab.active {
            background-color: #07c160;
            color: white;
        }
        .add-product-btn {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background-color: #07c160;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            bottom: 80px;
            right: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex">
            <i class="fas fa-signal mr-1"></i>
            <i class="fas fa-wifi mr-1"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="bg-white py-2 px-4 flex items-center">
        <div class="w-8">
            <i class="fas fa-arrow-left text-gray-600"></i>
        </div>
        <div class="text-center flex-1">
            <span class="font-medium text-lg">商品上传</span>
        </div>
        <div class="w-8">
            <i class="fas fa-ellipsis-v text-gray-600"></i>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="px-4 py-3 bg-white">
        <div class="flex justify-between text-xs text-gray-500 mb-2">
            <span>入驻进度</span>
            <span>5/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Category Tabs -->
    <div class="bg-white pt-2 pb-3 px-4">
        <div class="category-tabs">
            <div class="category-tab active">全部</div>
            <div class="category-tab">热销菜品</div>
            <div class="category-tab">主食</div>
            <div class="category-tab">小吃</div>
            <div class="category-tab">饮品</div>
            <div class="category-tab">套餐</div>
            <div class="category-tab">+ 新分类</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-4" style="height: calc(100vh - 200px);">
        <p class="text-gray-600 mb-3 text-xs">已上传 3 个商品，建议至少上传 5 个商品</p>

        <!-- Product 1 -->
        <div class="product-card">
            <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1562967914-608f82629710?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80')">
                <div class="product-actions">
                    <div class="action-btn">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-btn">
                        <i class="fas fa-trash"></i>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <div class="flex justify-between items-center mb-2">
                    <h3 class="font-medium">红烧牛肉面</h3>
                    <span class="text-red-500 font-medium">¥38</span>
                </div>
                <p class="text-xs text-gray-500 mb-2">精选牛肉，配以秘制汤底，口感鲜美</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-400">热销菜品</span>
                    <div class="flex items-center">
                        <span class="text-xs text-gray-400 mr-2">上架</span>
                        <div class="relative inline-block w-10 h-6 rounded-full bg-green-500">
                            <div class="absolute w-4 h-4 bg-white rounded-full top-1 right-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product 2 -->
        <div class="product-card">
            <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1563245372-f21724e3856d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80')">
                <div class="product-actions">
                    <div class="action-btn">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-btn">
                        <i class="fas fa-trash"></i>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <div class="flex justify-between items-center mb-2">
                    <h3 class="font-medium">宫保鸡丁</h3>
                    <span class="text-red-500 font-medium">¥42</span>
                </div>
                <p class="text-xs text-gray-500 mb-2">传统川菜，鸡肉鲜嫩，花生香脆</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-400">热销菜品</span>
                    <div class="flex items-center">
                        <span class="text-xs text-gray-400 mr-2">上架</span>
                        <div class="relative inline-block w-10 h-6 rounded-full bg-green-500">
                            <div class="absolute w-4 h-4 bg-white rounded-full top-1 right-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product 3 -->
        <div class="product-card">
            <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1551024709-8f23befc6f87?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80')">
                <div class="product-actions">
                    <div class="action-btn">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-btn">
                        <i class="fas fa-trash"></i>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <div class="flex justify-between items-center mb-2">
                    <h3 class="font-medium">冰镇柠檬茶</h3>
                    <span class="text-red-500 font-medium">¥15</span>
                </div>
                <p class="text-xs text-gray-500 mb-2">新鲜柠檬，清爽解腻</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-400">饮品</span>
                    <div class="flex items-center">
                        <span class="text-xs text-gray-400 mr-2">上架</span>
                        <div class="relative inline-block w-10 h-6 rounded-full bg-green-500">
                            <div class="absolute w-4 h-4 bg-white rounded-full top-1 right-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Button -->
    <div class="add-product-btn">
        <i class="fas fa-plus text-xl"></i>
    </div>

    <!-- Bottom Button -->
    <div class="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
        <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
            保存并继续
        </button>
    </div>
</body>
</html>
