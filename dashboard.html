<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析中心</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            width: 1440px;
            margin: 0;
            padding: 0;
        }
        .metric-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            margin-bottom: 16px;
        }
        .chart-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 16px;
            border: 2px dashed #cbd5e1;
        }
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }
        .ranking-item:hover {
            background: #f1f5f9;
            transform: translateX(4px);
        }
        .ranking-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin-right: 16px;
        }
        .ranking-1 { background: linear-gradient(135deg, #ffd700, #ffb347); }
        .ranking-2 { background: linear-gradient(135deg, #c0c0c0, #a8a8a8); }
        .ranking-3 { background: linear-gradient(135deg, #cd7f32, #b8860b); }
        .ranking-other { background: linear-gradient(135deg, #64748b, #475569); }
        .progress-bar {
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }
        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        .filter-btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            background: white;
            color: #64748b;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .filter-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        .filter-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        .trend-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
        }
        .trend-up {
            background: #dcfce7;
            color: #16a34a;
        }
        .trend-down {
            background: #fee2e2;
            color: #dc2626;
        }
        .data-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .table-header {
            background: #f8fafc;
            padding: 16px 24px;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #374151;
        }
        .table-row {
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
            transition: background 0.2s ease;
        }
        .table-row:hover {
            background: #f8fafc;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-8">
        <!-- 页面标题和筛选 -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">数据分析中心</h1>
                <p class="text-gray-500 mt-2">实时监控校园外卖平台运营数据</p>
            </div>
            <div class="flex gap-3">
                <button class="filter-btn active">今日</button>
                <button class="filter-btn">本周</button>
                <button class="filter-btn">本月</button>
                <button class="filter-btn">自定义</button>
            </div>
        </div>

        <!-- 核心指标卡片 -->
        <div class="grid grid-cols-4 gap-6 mb-8">
            <div class="metric-card">
                <div class="metric-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">1,247</div>
                <div class="text-sm text-gray-500 mb-2">今日订单数</div>
                <div class="trend-indicator trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +12.5%
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">¥28,456</div>
                <div class="text-sm text-gray-500 mb-2">今日收益</div>
                <div class="trend-indicator trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +8.3%
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-store"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">89</div>
                <div class="text-sm text-gray-500 mb-2">活跃商家</div>
                <div class="trend-indicator trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +5.6%
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2">96.8%</div>
                <div class="text-sm text-gray-500 mb-2">配送成功率</div>
                <div class="trend-indicator trend-down">
                    <i class="fas fa-arrow-down"></i>
                    -1.2%
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-2 gap-6 mb-8">
            <div class="chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-900">订单趋势</h3>
                    <div class="flex gap-2">
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-gray-600">订单量</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-600">收益</span>
                        </div>
                    </div>
                </div>
                <div class="chart-placeholder">
                    <div class="text-center">
                        <i class="fas fa-chart-line text-4xl mb-4"></i>
                        <div>订单趋势图表</div>
                        <div class="text-sm mt-2">集成 Chart.js 或 ECharts</div>
                    </div>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-900">商家分布</h3>
                    <select class="px-3 py-1 border border-gray-300 rounded-lg text-sm">
                        <option>按区域</option>
                        <option>按类型</option>
                        <option>按规模</option>
                    </select>
                </div>
                <div class="chart-placeholder">
                    <div class="text-center">
                        <i class="fas fa-chart-pie text-4xl mb-4"></i>
                        <div>商家分布饼图</div>
                        <div class="text-sm mt-2">集成 Chart.js 或 ECharts</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 排行榜和数据表格 -->
        <div class="grid grid-cols-3 gap-6">
            <!-- 商家销量排行 -->
            <div class="chart-container">
                <h3 class="text-xl font-bold text-gray-900 mb-6">商家销量排行</h3>
                <div class="space-y-3">
                    <div class="ranking-item">
                        <div class="ranking-number ranking-1">1</div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-900">麻辣香锅店</div>
                            <div class="text-sm text-gray-500">今日订单: 156单</div>
                            <div class="progress-bar">
                                <div class="progress-fill bg-gradient-to-r from-yellow-400 to-orange-500" style="width: 95%"></div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-900">¥4,680</div>
                            <div class="text-sm text-gray-500">收益</div>
                        </div>
                    </div>
                    
                    <div class="ranking-item">
                        <div class="ranking-number ranking-2">2</div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-900">新鲜水果店</div>
                            <div class="text-sm text-gray-500">今日订单: 142单</div>
                            <div class="progress-bar">
                                <div class="progress-fill bg-gradient-to-r from-gray-400 to-gray-600" style="width: 87%"></div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-900">¥3,560</div>
                            <div class="text-sm text-gray-500">收益</div>
                        </div>
                    </div>
                    
                    <div class="ranking-item">
                        <div class="ranking-number ranking-3">3</div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-900">便民超市</div>
                            <div class="text-sm text-gray-500">今日订单: 128单</div>
                            <div class="progress-bar">
                                <div class="progress-fill bg-gradient-to-r from-yellow-600 to-yellow-800" style="width: 78%"></div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-900">¥2,840</div>
                            <div class="text-sm text-gray-500">收益</div>
                        </div>
                    </div>
                    
                    <div class="ranking-item">
                        <div class="ranking-number ranking-other">4</div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-900">校园咖啡厅</div>
                            <div class="text-sm text-gray-500">今日订单: 95单</div>
                            <div class="progress-bar">
                                <div class="progress-fill bg-gradient-to-r from-gray-500 to-gray-700" style="width: 58%"></div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-900">¥2,280</div>
                            <div class="text-sm text-gray-500">收益</div>
                        </div>
                    </div>
                    
                    <div class="ranking-item">
                        <div class="ranking-number ranking-other">5</div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-900">快餐小食</div>
                            <div class="text-sm text-gray-500">今日订单: 87单</div>
                            <div class="progress-bar">
                                <div class="progress-fill bg-gradient-to-r from-gray-500 to-gray-700" style="width: 53%"></div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-900">¥1,740</div>
                            <div class="text-sm text-gray-500">收益</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配送数据统计 -->
            <div class="chart-container">
                <h3 class="text-xl font-bold text-gray-900 mb-6">配送数据统计</h3>
                <div class="space-y-6">
                    <div class="text-center p-6 bg-blue-50 rounded-xl">
                        <div class="text-3xl font-bold text-blue-600 mb-2">15.6分钟</div>
                        <div class="text-sm text-gray-600">平均配送时间</div>
                        <div class="trend-indicator trend-up mt-2">
                            <i class="fas fa-arrow-up"></i>
                            较昨日快2.3分钟
                        </div>
                    </div>
                    
                    <div class="text-center p-6 bg-green-50 rounded-xl">
                        <div class="text-3xl font-bold text-green-600 mb-2">96.8%</div>
                        <div class="text-sm text-gray-600">准时送达率</div>
                        <div class="trend-indicator trend-down mt-2">
                            <i class="fas fa-arrow-down"></i>
                            较昨日降低1.2%
                        </div>
                    </div>
                    
                    <div class="text-center p-6 bg-purple-50 rounded-xl">
                        <div class="text-3xl font-bold text-purple-600 mb-2">4.7分</div>
                        <div class="text-sm text-gray-600">用户满意度</div>
                        <div class="trend-indicator trend-up mt-2">
                            <i class="fas fa-arrow-up"></i>
                            较昨日提升0.2分
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时数据 -->
            <div class="chart-container">
                <h3 class="text-xl font-bold text-gray-900 mb-6">实时数据</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-sm font-medium">在线商家</span>
                        </div>
                        <span class="font-bold text-gray-900">67</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                            <span class="text-sm font-medium">进行中订单</span>
                        </div>
                        <span class="font-bold text-gray-900">234</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                            <span class="text-sm font-medium">配送中订单</span>
                        </div>
                        <span class="font-bold text-gray-900">156</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                            <span class="text-sm font-medium">异常订单</span>
                        </div>
                        <span class="font-bold text-red-600">8</span>
                    </div>
                    
                    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-center gap-2 mb-2">
                            <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                            <span class="font-medium text-yellow-800">系统提醒</span>
                        </div>
                        <div class="text-sm text-yellow-700">
                            检测到3个商家配送时间超时，建议及时处理
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
