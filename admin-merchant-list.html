<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家列表</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }
        
        .admin-layout {
            display: flex;
            height: calc(100vh - 44px);
        }
        
        .sidebar {
            width: 80px;
            background-color: #001529;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 0;
            overflow-y: auto;
        }
        
        .sidebar-logo {
            width: 40px;
            height: 40px;
            background-color: #1890ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            color: white;
            font-size: 20px;
        }
        
        .sidebar-menu {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        
        .menu-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            color: rgba(255, 255, 255, 0.65);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: white;
        }
        
        .menu-item.active {
            color: white;
            background-color: #1890ff;
        }
        
        .menu-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-left: 16px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .header-icon:hover {
            color: #1890ff;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 16px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .page-actions {
            display: flex;
            align-items: center;
        }
        
        .action-button {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .action-button:hover {
            background-color: #40a9ff;
        }
        
        .action-button i {
            margin-right: 8px;
        }
        
        .filter-card {
            background-color: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        
        .form-select:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .filter-buttons {
            display: flex;
            justify-content: flex-end;
            margin-top: 16px;
        }
        
        .filter-button {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 8px;
        }
        
        .filter-reset {
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .filter-reset:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .filter-submit {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        
        .filter-submit:hover {
            background-color: #40a9ff;
        }
        
        .table-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .merchant-info {
            display: flex;
            align-items: center;
        }
        
        .merchant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #999;
            font-size: 20px;
        }
        
        .merchant-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .merchant-id {
            font-size: 12px;
            color: #999;
        }
        
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
        
        .table-actions {
            display: flex;
            align-items: center;
        }
        
        .table-action {
            color: #1890ff;
            margin-right: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .table-action:hover {
            color: #40a9ff;
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-top: 16px;
        }
        
        .page-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 4px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-item:hover {
            background-color: #f5f5f5;
        }
        
        .page-item.active {
            background-color: #1890ff;
            color: white;
        }
        
        .page-prev, .page-next {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 4px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-prev:hover, .page-next:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-store"></i>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user-check menu-icon"></i>
                    <span>审核</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span>统计</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>设置</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <i class="fas fa-bars" style="font-size: 18px; color: #666; cursor: pointer;"></i>
                    <div class="header-title">商家管理</div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">5</div>
                    </div>
                    <div class="user-avatar">
                        <span>管</span>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <div class="page-title">商家列表</div>
                    <div class="page-actions">
                        <button class="action-button">
                            <i class="fas fa-download"></i>
                            <span>导出数据</span>
                        </button>
                    </div>
                </div>
                
                <div class="filter-card">
                    <div class="filter-form">
                        <div class="form-group">
                            <label class="form-label">商家名称</label>
                            <input type="text" class="form-input" placeholder="请输入商家名称">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">行业类型</label>
                            <select class="form-select">
                                <option value="">全部</option>
                                <option value="1">餐饮</option>
                                <option value="2">零售</option>
                                <option value="3">美容美发</option>
                                <option value="4">健身</option>
                                <option value="5">教育</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select class="form-select">
                                <option value="">全部</option>
                                <option value="1">待审核</option>
                                <option value="2">已通过</option>
                                <option value="3">已拒绝</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">入驻时间</label>
                            <input type="date" class="form-input">
                        </div>
                    </div>
                    
                    <div class="filter-buttons">
                        <button class="filter-button filter-reset">重置</button>
                        <button class="filter-button filter-submit">查询</button>
                    </div>
                </div>
                
                <div class="table-card">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>商家信息</th>
                                <th>行业类型</th>
                                <th>联系人</th>
                                <th>入驻时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-store"></i>
                                        </div>
                                        <div>
                                            <div class="merchant-name">品味小厨</div>
                                            <div class="merchant-id">ID: 10001</div>
                                        </div>
                                    </div>
                                </td>
                                <td>餐饮</td>
                                <td>张先生 (13812345678)</td>
                                <td>2023-05-18 14:23</td>
                                <td><span class="status-tag status-pending">待审核</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="table-action">查看</a>
                                        <a href="#" class="table-action">审核</a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-store"></i>
                                        </div>
                                        <div>
                                            <div class="merchant-name">鲜花坊</div>
                                            <div class="merchant-id">ID: 10002</div>
                                        </div>
                                    </div>
                                </td>
                                <td>零售</td>
                                <td>李小姐 (13987654321)</td>
                                <td>2023-05-18 11:05</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="table-action">查看</a>
                                        <a href="#" class="table-action">编辑</a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-store"></i>
                                        </div>
                                        <div>
                                            <div class="merchant-name">美丽发廊</div>
                                            <div class="merchant-id">ID: 10003</div>
                                        </div>
                                    </div>
                                </td>
                                <td>美容美发</td>
                                <td>王女士 (13765432198)</td>
                                <td>2023-05-17 16:42</td>
                                <td><span class="status-tag status-rejected">已拒绝</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="table-action">查看</a>
                                        <a href="#" class="table-action">重新审核</a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-store"></i>
                                        </div>
                                        <div>
                                            <div class="merchant-name">健身工作室</div>
                                            <div class="merchant-id">ID: 10004</div>
                                        </div>
                                    </div>
                                </td>
                                <td>健身</td>
                                <td>刘先生 (13612345678)</td>
                                <td>2023-05-17 09:18</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="table-action">查看</a>
                                        <a href="#" class="table-action">编辑</a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-store"></i>
                                        </div>
                                        <div>
                                            <div class="merchant-name">快乐童装</div>
                                            <div class="merchant-id">ID: 10005</div>
                                        </div>
                                    </div>
                                </td>
                                <td>零售</td>
                                <td>赵女士 (13587654321)</td>
                                <td>2023-05-16 15:37</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="table-action">查看</a>
                                        <a href="#" class="table-action">编辑</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination">
                        <div class="page-prev">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="page-item active">1</div>
                        <div class="page-item">2</div>
                        <div class="page-item">3</div>
                        <div class="page-item">4</div>
                        <div class="page-item">5</div>
                        <div class="page-next">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
