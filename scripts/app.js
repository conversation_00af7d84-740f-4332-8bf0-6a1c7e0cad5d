// 主应用逻辑
document.addEventListener('DOMContentLoaded', () => {
    // 初始化状态栏时间
    updateStatusBarTime();
    
    // 视频播放控制
    initializeVideoPlayers();
    
    // 点赞/评论/分享/收藏功能
    initializeSocialActions();
    
    // 下拉刷新和无限滚动
    initializeInfiniteScroll();
    
    // 初始化评论系统
    initializeCommentSystem();
});

// 更新状态栏时间
function updateStatusBarTime() {
    const timeElement = document.querySelector('.status-bar__time');
    if (timeElement) {
        const updateTime = () => {
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        };
        updateTime();
        setInterval(updateTime, 60000);
    }
}

// 初始化视频播放器
function initializeVideoPlayers() {
    const videos = document.querySelectorAll('video');
    let currentPlayingVideo = null;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const video = entry.target;
                if (currentPlayingVideo && currentPlayingVideo !== video) {
                    currentPlayingVideo.pause();
                }
                video.play();
                currentPlayingVideo = video;
            } else {
                entry.target.pause();
            }
        });
    }, { threshold: 0.6 });

    videos.forEach(video => {
        observer.observe(video);
        
        // 双击点赞
        let lastTap = 0;
        video.addEventListener('touchend', (e) => {
            const currentTime = new Date().getTime();
            const tapLength = currentTime - lastTap;
            if (tapLength < 300 && tapLength > 0) {
                // 创建一个大爱心动画
                const heart = document.createElement('div');
                heart.className = 'big-heart-animation';
                heart.innerHTML = '<i class="fas fa-heart"></i>';
                video.parentElement.appendChild(heart);
                
                // 触发点赞
                triggerLike(video.closest('.video-card'));
                
                // 动画结束后移除
                setTimeout(() => heart.remove(), 1000);
            }
            lastTap = currentTime;
        });
    });
}

// 平滑滚动到视频
function scrollToVideo(videoElement) {
    const rect = videoElement.getBoundingClientRect();
    const targetScroll = window.scrollY + rect.top - (window.innerHeight - rect.height) / 2;
    
    window.scrollTo({
        top: targetScroll,
        behavior: 'smooth'
    });
}

// 增强版视频播放控制
function enhancedVideoControl() {
    const videos = document.querySelectorAll('video');
    let activeVideo = null;
    
    const videoObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const video = entry.target;
            
            // 当视频超过50%可见时
            if (entry.isIntersecting && entry.intersectionRatio >= 0.5) {
                if (activeVideo && activeVideo !== video) {
                    activeVideo.pause();
                }
                video.play().catch(e => console.log('自动播放失败:', e));
                activeVideo = video;
                
                // 添加加载动画
                const loadingSpinner = video.parentElement.querySelector('.video-loading');
                if (loadingSpinner) {
                    loadingSpinner.style.display = 'flex';
                }
            } else {
                video.pause();
            }
        });
    }, {
        threshold: [0.5]
    });
    
    videos.forEach(video => {
        // 添加加载动画
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'video-loading';
        loadingSpinner.innerHTML = '<div class="loading__spinner"></div>';
        video.parentElement.appendChild(loadingSpinner);
        
        // 视频加载完成时
        video.addEventListener('canplay', () => {
            const loadingSpinner = video.parentElement.querySelector('.video-loading');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        });
        
        // 监听视频可见性
        videoObserver.observe(video);
    });
}

// 初始化社交功能
function initializeSocialActions() {
    document.querySelectorAll('.video-card__actions').forEach(actionBar => {
        actionBar.addEventListener('click', (e) => {
            const action = e.target.closest('.video-card__action');
            if (!action) return;
            
            const icon = action.querySelector('i');
            const counter = action.querySelector('span');
            
            if (icon.classList.contains('fa-heart')) {
                toggleLike(icon, counter);
            } else if (icon.classList.contains('fa-comment')) {
                showComments(action.closest('.video-card'));
            } else if (icon.classList.contains('fa-share')) {
                showShareOptions();
            } else if (icon.classList.contains('fa-bookmark')) {
                toggleBookmark(icon);
            }
        });
    });
}

// 点赞功能
function toggleLike(icon, counter) {
    const isLiked = icon.classList.contains('text-danger');
    icon.classList.toggle('text-danger');
    
    let count = parseInt(counter.textContent);
    counter.textContent = isLiked ? count - 1 : count + 1;
    
    // 添加点赞动画
    if (!isLiked) {
        const heart = document.createElement('div');
        heart.className = 'floating-heart';
        icon.parentElement.appendChild(heart);
        setTimeout(() => heart.remove(), 1000);
    }
}

// 评论系统
function initializeCommentSystem() {
    let currentVideoCard = null;
    
    // 显示评论面板
    function showComments(videoCard) {
        currentVideoCard = videoCard;
        
        // 创建遮罩层
        const overlay = document.createElement('div');
        overlay.className = 'comments-overlay';
        document.body.appendChild(overlay);
        
        // 创建评论面板
        const commentsPanel = document.createElement('div');
        commentsPanel.className = 'comments-panel';
        
        // 模拟评论数据
        const comments = [
            {
                id: 1,
                username: "美食爱好者",
                avatar: "https://via.placeholder.com/36",
                content: "这道菜看起来太诱人了！请问具体的步骤是怎样的？",
                time: "5分钟前",
                likes: 12
            },
            {
                id: 2,
                username: "厨艺达人",
                avatar: "https://via.placeholder.com/36",
                content: "配料可以选择新鲜的应季蔬菜，会更加美味哦～",
                time: "3分钟前",
                likes: 8
            }
        ];
        
        commentsPanel.innerHTML = `
            <div class="comments-header">
                <h3>评论 (${comments.length})</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="comments-list">
                ${comments.map(comment => `
                    <div class="comment-item" data-id="${comment.id}">
                        <img src="${comment.avatar}" alt="${comment.username}" class="comment-item__avatar">
                        <div class="comment-item__content">
                            <div class="comment-item__header">
                                <span class="comment-item__username">${comment.username}</span>
                                <span class="comment-item__time">${comment.time}</span>
                            </div>
                            <p class="comment-item__text">${comment.content}</p>
                            <div class="comment-item__actions">
                                <div class="comment-action like-action">
                                    <i class="far fa-heart"></i>
                                    <span>${comment.likes}</span>
                                </div>
                                <div class="comment-action">
                                    <i class="far fa-comment"></i>
                                    <span>回复</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="comment-input-container">
                <input type="text" class="comment-input" placeholder="说点什么...">
                <button class="send-btn" disabled>发送</button>
            </div>
        `;
        
        document.body.appendChild(commentsPanel);
        
        // 显示动画
        requestAnimationFrame(() => {
            overlay.classList.add('comments-overlay--active');
            commentsPanel.classList.add('comments-panel--active');
        });
        
        // 绑定关闭事件
        const closeBtn = commentsPanel.querySelector('.close-btn');
        const closePanel = () => {
            overlay.classList.remove('comments-overlay--active');
            commentsPanel.classList.remove('comments-panel--active');
            setTimeout(() => {
                overlay.remove();
                commentsPanel.remove();
            }, 300);
        };
        
        closeBtn.addEventListener('click', closePanel);
        overlay.addEventListener('click', closePanel);
        
        // 输入框交互
        const input = commentsPanel.querySelector('.comment-input');
        const sendBtn = commentsPanel.querySelector('.send-btn');
        
        input.addEventListener('input', () => {
            sendBtn.disabled = !input.value.trim();
        });
        
        // 发送评论
        sendBtn.addEventListener('click', () => {
            const content = input.value.trim();
            if (!content) return;
            
            // 创建新评论
            const newComment = {
                id: Date.now(),
                username: "我",
                avatar: "https://via.placeholder.com/36",
                content: content,
                time: "刚刚",
                likes: 0
            };
            
            // 添加新评论到列表
            const commentsList = commentsPanel.querySelector('.comments-list');
            const commentElement = document.createElement('div');
            commentElement.className = 'comment-item';
            commentElement.setAttribute('data-id', newComment.id);
            commentElement.innerHTML = `
                <img src="${newComment.avatar}" alt="${newComment.username}" class="comment-item__avatar">
                <div class="comment-item__content">
                    <div class="comment-item__header">
                        <span class="comment-item__username">${newComment.username}</span>
                        <span class="comment-item__time">${newComment.time}</span>
                    </div>
                    <p class="comment-item__text">${newComment.content}</p>
                    <div class="comment-item__actions">
                        <div class="comment-action like-action">
                            <i class="far fa-heart"></i>
                            <span>0</span>
                        </div>
                        <div class="comment-action">
                            <i class="far fa-comment"></i>
                            <span>回复</span>
                        </div>
                    </div>
                </div>
            `;
            
            commentsList.insertBefore(commentElement, commentsList.firstChild);
            input.value = '';
            sendBtn.disabled = true;
            
            // 更新评论数
            const commentCount = commentsPanel.querySelector('.comments-header h3');
            const currentCount = parseInt(commentCount.textContent.match(/\d+/)[0]);
            commentCount.textContent = `评论 (${currentCount + 1})`;
        });
        
        // 点赞评论
        commentsPanel.addEventListener('click', (e) => {
            const likeAction = e.target.closest('.like-action');
            if (likeAction) {
                const icon = likeAction.querySelector('i');
                const count = likeAction.querySelector('span');
                const isLiked = icon.classList.contains('fas');
                
                icon.className = isLiked ? 'far fa-heart' : 'fas fa-heart';
                count.textContent = isLiked ? 
                    parseInt(count.textContent) - 1 : 
                    parseInt(count.textContent) + 1;
                
                if (!isLiked) {
                    icon.style.color = 'var(--primary-color)';
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1)';
                    }, 200);
                } else {
                    icon.style.color = '';
                }
            }
        });
    }
    
    // 绑定评论按钮点击事件
    document.querySelectorAll('.video-card__action').forEach(action => {
        const icon = action.querySelector('i');
        if (icon && icon.classList.contains('fa-comment')) {
            action.addEventListener('click', () => {
                showComments(action.closest('.video-card'));
            });
        }
    });
}

// 分享选项
function showShareOptions() {
    const shareOptions = [
        { icon: 'weixin', name: '微信' },
        { icon: 'weibo', name: '微博' },
        { icon: 'qq', name: 'QQ' },
        { icon: 'link', name: '复制链接' }
    ];
    
    const shareSheet = document.createElement('div');
    shareSheet.className = 'share-sheet slide-up';
    shareSheet.innerHTML = `
        <div class="share-options">
            ${shareOptions.map(option => `
                <div class="share-option">
                    <i class="fab fa-${option.icon}"></i>
                    <span>${option.name}</span>
                </div>
            `).join('')}
        </div>
        <button class="cancel-btn">取消</button>
    `;
    
    document.body.appendChild(shareSheet);
    setTimeout(() => shareSheet.classList.add('slide-up-active'), 10);
    
    shareSheet.querySelector('.cancel-btn').onclick = () => {
        shareSheet.classList.remove('slide-up-active');
        setTimeout(() => shareSheet.remove(), 300);
    };
}

// 收藏功能
function toggleBookmark(icon) {
    icon.classList.toggle('text-warning');
    // TODO: 调用收藏API
}

// 初始化无限滚动
function initializeInfiniteScroll() {
    const container = document.querySelector('.container');
    let loading = false;
    
    const loadMoreContent = () => {
        if (loading) return;
        loading = true;
        
        // 显示加载动画
        document.querySelector('.loading').style.display = 'flex';
        
        // 模拟API请求
        setTimeout(() => {
            // TODO: 真实环境中这里会调用后端API
            const newContent = createVideoCard();
            container.insertBefore(newContent, document.querySelector('.loading'));
            
            loading = false;
            document.querySelector('.loading').style.display = 'none';
        }, 1500);
    };
    
    window.addEventListener('scroll', () => {
        if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 1000) {
            loadMoreContent();
        }
    });
}

// 创建视频卡片模板
function createVideoCard() {
    const card = document.createElement('div');
    card.className = 'video-card fade-enter';
    // TODO: 根据API返回数据填充内容
    setTimeout(() => card.classList.add('fade-enter-active'), 10);
    return card;
}
