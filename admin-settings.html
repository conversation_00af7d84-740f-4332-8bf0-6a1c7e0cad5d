<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }
        
        .admin-layout {
            display: flex;
            height: calc(100vh - 44px);
        }
        
        .sidebar {
            width: 80px;
            background-color: #001529;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 0;
            overflow-y: auto;
        }
        
        .sidebar-logo {
            width: 40px;
            height: 40px;
            background-color: #1890ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            color: white;
            font-size: 20px;
        }
        
        .sidebar-menu {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        
        .menu-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            color: rgba(255, 255, 255, 0.65);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: white;
        }
        
        .menu-item.active {
            color: white;
            background-color: #1890ff;
        }
        
        .menu-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-left: 16px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .header-icon:hover {
            color: #1890ff;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 16px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .settings-layout {
            display: flex;
            gap: 24px;
        }
        
        .settings-sidebar {
            width: 200px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .settings-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .settings-menu-item {
            padding: 12px 16px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .settings-menu-item:hover {
            color: #1890ff;
            background-color: #f5f5f5;
        }
        
        .settings-menu-item.active {
            color: #1890ff;
            background-color: #e6f7ff;
            border-left-color: #1890ff;
        }
        
        .settings-content {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            padding: 24px;
        }
        
        .settings-section {
            margin-bottom: 24px;
        }
        
        .settings-section:last-child {
            margin-bottom: 0;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        
        .form-select:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
            resize: vertical;
            min-height: 80px;
        }
        
        .form-textarea:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-hint {
            font-size: 12px;
            color: #999;
            margin-top: 4px;
        }
        
        .form-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }
        
        .form-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }
        
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .switch-slider {
            background-color: #1890ff;
        }
        
        input:checked + .switch-slider:before {
            transform: translateX(20px);
        }
        
        .switch-label {
            font-size: 14px;
            color: #333;
            margin-left: 8px;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 24px;
        }
        
        .form-button {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 8px;
        }
        
        .button-default {
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .button-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .button-primary {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        
        .button-primary:hover {
            background-color: #40a9ff;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-store"></i>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user-check menu-icon"></i>
                    <span>审核</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span>统计</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>设置</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <i class="fas fa-bars" style="font-size: 18px; color: #666; cursor: pointer;"></i>
                    <div class="header-title">系统设置</div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">5</div>
                    </div>
                    <div class="user-avatar">
                        <span>管</span>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <div class="page-title">系统设置</div>
                </div>
                
                <div class="settings-layout">
                    <div class="settings-sidebar">
                        <ul class="settings-menu">
                            <li class="settings-menu-item active">基本设置</li>
                            <li class="settings-menu-item">审核设置</li>
                            <li class="settings-menu-item">通知设置</li>
                            <li class="settings-menu-item">安全设置</li>
                            <li class="settings-menu-item">权限管理</li>
                            <li class="settings-menu-item">日志管理</li>
                        </ul>
                    </div>
                    
                    <div class="settings-content">
                        <div class="settings-section">
                            <div class="section-title">基本信息</div>
                            
                            <div class="form-group">
                                <label class="form-label">系统名称</label>
                                <input type="text" class="form-input" value="商家入驻系统管理后台">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">系统描述</label>
                                <textarea class="form-textarea">商家入驻系统管理后台是一个用于管理商家入驻、审核和运营的平台。</textarea>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">系统Logo</label>
                                <div class="flex items-center">
                                    <div class="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <button class="form-button button-default">上传新Logo</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <div class="section-title">入驻设置</div>
                            
                            <div class="form-group">
                                <label class="form-label">开放入驻</label>
                                <div class="flex items-center">
                                    <label class="form-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                    <span class="switch-label">允许新商家申请入驻</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">入驻行业</label>
                                <select class="form-select">
                                    <option value="1">全部开放</option>
                                    <option value="2">部分开放</option>
                                    <option value="3">暂停开放</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">入驻区域</label>
                                <select class="form-select">
                                    <option value="1">全国</option>
                                    <option value="2">部分城市</option>
                                    <option value="3">指定区域</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">入驻审核</label>
                                <div class="flex items-center">
                                    <label class="form-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                    <span class="switch-label">开启人工审核</span>
                                </div>
                                <div class="form-hint">关闭后将自动通过所有入驻申请</div>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <div class="section-title">系统维护</div>
                            
                            <div class="form-group">
                                <label class="form-label">系统状态</label>
                                <select class="form-select">
                                    <option value="1">正常运行</option>
                                    <option value="2">维护模式</option>
                                    <option value="3">紧急维护</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">维护公告</label>
                                <textarea class="form-textarea" placeholder="系统维护中，请稍后再试..."></textarea>
                                <div class="form-hint">仅在维护模式下显示</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">数据备份</label>
                                <div class="flex items-center">
                                    <label class="form-switch">
                                        <input type="checkbox" checked>
                                        <span class="switch-slider"></span>
                                    </label>
                                    <span class="switch-label">启用自动备份</span>
                                </div>
                                <div class="form-hint">每天凌晨3点自动备份数据</div>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button class="form-button button-default">取消</button>
                            <button class="form-button button-primary">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
