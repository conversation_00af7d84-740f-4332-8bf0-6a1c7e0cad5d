<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>视频详情</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .video-detail {
            padding-top: 44px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .video-player {
            width: 100%;
            aspect-ratio: 9/16;
            background: #000;
            position: relative;
        }
        
        .video-info {
            padding: 16px;
            background: #fff;
            border-bottom: 1px solid #E8E8E8;
        }
        
        .author-info {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .video-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 12px;
        }
        
        .video-stats {
            display: flex;
            gap: 16px;
            color: var(--text-secondary);
            font-size: 12px;
        }
        
        .comments-container {
            flex: 1;
            overflow-y: auto;
            background: #fff;
        }
        
        .comment-input {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 8px 16px;
            background: #fff;
            border-top: 1px solid #E8E8E8;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .comment-input input {
            flex: 1;
            border: none;
            background: #F5F5F5;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .comment-input button {
            background: var(--primary-color);
            color: #fff;
            border: none;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <i class="fas fa-arrow-left" onclick="history.back()"></i>
        <div class="status-bar__title">视频详情</div>
        <div class="status-bar__icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <div class="video-detail">
        <div class="video-player">
            <video width="100%" controls autoplay>
                <source src="video.mp4" type="video/mp4">
                您的浏览器不支持视频播放
            </video>
        </div>

        <div class="video-info">
            <div class="author-info">
                <img src="https://via.placeholder.com/40" alt="作者头像" class="video-card__avatar">
                <div class="video-card__info">
                    <div class="video-card__username">美食达人</div>
                    <div class="video-card__time">2小时前</div>
                </div>
                <button class="follow-btn btn-hover">关注</button>
            </div>
            
            <div class="video-description">
                今天给大家分享一道超级美味的家常菜，简单易学，新手也能轻松掌握！#美食 #家常菜 #教程
            </div>
            
            <div class="video-stats">
                <span><i class="fas fa-play"></i> 10.2万播放</span>
                <span><i class="fas fa-heart"></i> 2.3k赞</span>
                <span><i class="fas fa-comment"></i> 456评论</span>
                <span><i class="fas fa-share"></i> 89分享</span>
            </div>
        </div>

        <div class="comments-container">
            <!-- 评论列表 -->
            <div class="comments-section">
                <div class="comment-item">
                    <img src="https://via.placeholder.com/32" alt="评论者头像" class="comment-item__avatar">
                    <div class="comment-item__content">
                        <div class="comment-item__username">用户1</div>
                        <div class="comment-item__text">看起来太美味了！一定要试试看！</div>
                        <div class="comment-item__actions">
                            <span class="comment-time">10分钟前</span>
                            <span class="comment-like"><i class="fas fa-heart"></i> 12</span>
                            <span class="comment-reply">回复</span>
                        </div>
                    </div>
                </div>
                <!-- 更多评论... -->
            </div>
        </div>

        <div class="comment-input">
            <input type="text" placeholder="说点什么...">
            <button class="btn-hover">发送</button>
        </div>
    </div>

    <script src="scripts/app.js"></script>
</body>
</html>
