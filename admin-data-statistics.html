<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据统计</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }
        .chart-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 24px;
        }
        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 16px;
            border: 2px dashed #cbd5e1;
        }
        .trend-up {
            color: #10b981;
        }
        .trend-down {
            color: #ef4444;
        }
        .filter-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 24px;
        }
        .form-input, .form-select {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.2s;
            background: #fafbfc;
        }
        .form-input:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
            background: white;
        }
        .btn {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            margin-bottom: 12px;
            transition: all 0.2s;
        }
        .ranking-item:hover {
            background: #f1f5f9;
            transform: translateX(4px);
        }
        .ranking-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin-right: 16px;
        }
        .rank-1 { background: linear-gradient(135deg, #fbbf24, #f59e0b); }
        .rank-2 { background: linear-gradient(135deg, #9ca3af, #6b7280); }
        .rank-3 { background: linear-gradient(135deg, #f97316, #ea580c); }
        .rank-other { background: linear-gradient(135deg, #64748b, #475569); }
        .progress-bar {
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 时间筛选 -->
        <div class="filter-card">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">数据统计分析</h3>
                <div class="flex gap-4 items-center">
                    <select class="form-select">
                        <option>今日</option>
                        <option>昨日</option>
                        <option>本周</option>
                        <option>本月</option>
                        <option>自定义</option>
                    </select>
                    <input type="date" class="form-input">
                    <span class="text-gray-500">至</span>
                    <input type="date" class="form-input">
                    <button class="btn btn-primary">
                        <i class="fas fa-search mr-2"></i>查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 核心指标 -->
        <div class="grid grid-cols-4 gap-6 mb-6">
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm mb-2">总订单数</p>
                        <p class="text-3xl font-bold text-gray-900">2,847</p>
                        <p class="text-sm mt-2">
                            <span class="trend-up">
                                <i class="fas fa-arrow-up mr-1"></i>+12.5%
                            </span>
                            <span class="text-gray-500">较昨日</span>
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-blue-600 text-2xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm mb-2">总收益</p>
                        <p class="text-3xl font-bold text-gray-900">¥45,680</p>
                        <p class="text-sm mt-2">
                            <span class="trend-up">
                                <i class="fas fa-arrow-up mr-1"></i>+8.3%
                            </span>
                            <span class="text-gray-500">较昨日</span>
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-yen-sign text-green-600 text-2xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm mb-2">活跃商家</p>
                        <p class="text-3xl font-bold text-gray-900">128</p>
                        <p class="text-sm mt-2">
                            <span class="trend-up">
                                <i class="fas fa-arrow-up mr-1"></i>+5.2%
                            </span>
                            <span class="text-gray-500">较昨日</span>
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-store text-purple-600 text-2xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm mb-2">配送成功率</p>
                        <p class="text-3xl font-bold text-gray-900">89.2%</p>
                        <p class="text-sm mt-2">
                            <span class="trend-down">
                                <i class="fas fa-arrow-down mr-1"></i>-2.1%
                            </span>
                            <span class="text-gray-500">较昨日</span>
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-motorcycle text-orange-600 text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-2 gap-6 mb-6">
            <div class="chart-container">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">订单趋势分析</h3>
                <div class="chart-placeholder">
                    <div class="text-center">
                        <i class="fas fa-chart-line text-4xl mb-4"></i>
                        <p>订单数量趋势图</p>
                        <p class="text-sm">显示最近7天的订单变化趋势</p>
                    </div>
                </div>
            </div>
            
            <div class="chart-container">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">收益分析</h3>
                <div class="chart-placeholder">
                    <div class="text-center">
                        <i class="fas fa-chart-bar text-4xl mb-4"></i>
                        <p>收益统计图</p>
                        <p class="text-sm">显示各时段收益分布情况</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 排行榜和详细数据 -->
        <div class="grid grid-cols-2 gap-6">
            <!-- 商家排行榜 -->
            <div class="chart-container">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">商家销量排行榜</h3>
                <div class="space-y-3">
                    <div class="ranking-item">
                        <div class="ranking-number rank-1">1</div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <span class="font-semibold text-gray-900">麻辣香锅店</span>
                                <span class="text-lg font-bold text-gray-900">¥8,560</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%"></div>
                            </div>
                            <div class="flex justify-between text-sm text-gray-500 mt-1">
                                <span>订单: 156单</span>
                                <span>增长: +15.2%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ranking-item">
                        <div class="ranking-number rank-2">2</div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <span class="font-semibold text-gray-900">新鲜水果店</span>
                                <span class="text-lg font-bold text-gray-900">¥6,420</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 75%"></div>
                            </div>
                            <div class="flex justify-between text-sm text-gray-500 mt-1">
                                <span>订单: 128单</span>
                                <span>增长: +8.7%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ranking-item">
                        <div class="ranking-number rank-3">3</div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <span class="font-semibold text-gray-900">便民超市</span>
                                <span class="text-lg font-bold text-gray-900">¥5,280</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 62%"></div>
                            </div>
                            <div class="flex justify-between text-sm text-gray-500 mt-1">
                                <span>订单: 98单</span>
                                <span>增长: +12.1%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ranking-item">
                        <div class="ranking-number rank-other">4</div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <span class="font-semibold text-gray-900">校园奶茶店</span>
                                <span class="text-lg font-bold text-gray-900">¥4,150</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 48%"></div>
                            </div>
                            <div class="flex justify-between text-sm text-gray-500 mt-1">
                                <span>订单: 89单</span>
                                <span>增长: +6.3%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ranking-item">
                        <div class="ranking-number rank-other">5</div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <span class="font-semibold text-gray-900">快餐小厨</span>
                                <span class="text-lg font-bold text-gray-900">¥3,890</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 45%"></div>
                            </div>
                            <div class="flex justify-between text-sm text-gray-500 mt-1">
                                <span>订单: 76单</span>
                                <span>增长: +3.8%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配送数据统计 -->
            <div class="chart-container">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">配送数据统计</h3>
                <div class="space-y-6">
                    <div class="bg-gray-50 rounded-12 p-4">
                        <h4 class="font-semibold text-gray-900 mb-3">分销公司表现</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700">校园配送有限公司</span>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-gray-900">1,456单</div>
                                    <div class="text-xs text-green-600">成功率: 92.3%</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700">快递小哥配送</span>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-gray-900">1,391单</div>
                                    <div class="text-xs text-green-600">成功率: 88.7%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-12 p-4">
                        <h4 class="font-semibold text-gray-900 mb-3">配送时效统计</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-700">30分钟内</span>
                                <span class="font-semibold text-green-600">45.2%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-700">30-45分钟</span>
                                <span class="font-semibold text-blue-600">38.7%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-700">45-60分钟</span>
                                <span class="font-semibold text-orange-600">12.8%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-700">超过60分钟</span>
                                <span class="font-semibold text-red-600">3.3%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-12 p-4">
                        <h4 class="font-semibold text-gray-900 mb-3">用户满意度</h4>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600 mb-2">4.6</div>
                            <div class="flex justify-center mb-2">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star-half-alt text-yellow-400"></i>
                            </div>
                            <div class="text-sm text-gray-500">基于2,847条评价</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
