<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校园外卖产品后台管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            width: 1440px;
            margin: 0 auto;
            min-height: 100vh;
        }
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 28px;
            font-weight: bold;
            color: #333;
        }
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        .header-stats {
            display: flex;
            gap: 30px;
            align-items: center;
        }
        .stat-card {
            text-align: center;
            padding: 12px 20px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.2);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        .page-section {
            background: white;
            margin: 30px 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .section-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px 40px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .section-icon {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        .section-title {
            font-size: 24px;
            font-weight: bold;
        }
        .section-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
        }
        iframe {
            border: none;
            width: 1440px;
            height: 900px;
            display: block;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #333;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .notification-badge {
            position: relative;
        }
        .notification-badge::after {
            content: '3';
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="logo">
            <div class="logo-icon">
                <i class="fas fa-utensils"></i>
            </div>
            <div>
                <div>校园外卖产品后台</div>
                <div style="font-size: 14px; color: #666; font-weight: normal;">Campus Food Delivery Admin</div>
            </div>
        </div>
        
        <div class="header-stats">
            <div class="stat-card">
                <div class="stat-number">1,247</div>
                <div class="stat-label">今日订单</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89</div>
                <div class="stat-label">活跃商家</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">¥28,456</div>
                <div class="stat-label">今日收益</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">96.8%</div>
                <div class="stat-label">配送成功率</div>
            </div>
        </div>
        
        <div class="user-info">
            <div class="notification-badge">
                <i class="fas fa-bell text-xl text-gray-600 cursor-pointer hover:text-blue-600"></i>
            </div>
            <div class="user-avatar">A</div>
            <div>
                <div class="font-semibold">管理员</div>
                <div class="text-sm text-gray-500"><EMAIL></div>
            </div>
        </div>
    </div>

    <!-- 数据分析页面 -->
    <div class="page-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div>
                <div class="section-title">数据分析中心</div>
                <div class="section-subtitle">营业数据、商家数据、配送数据统计分析</div>
            </div>
        </div>
        <iframe src="dashboard.html"></iframe>
    </div>

    <!-- 商家管理页面 -->
    <div class="page-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-store"></i>
            </div>
            <div>
                <div class="section-title">商家管理</div>
                <div class="section-subtitle">商家信息管理、审核、状态管理</div>
            </div>
        </div>
        <iframe src="merchants.html"></iframe>
    </div>

    <!-- 订单管理页面 -->
    <div class="page-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div>
                <div class="section-title">订单管理</div>
                <div class="section-subtitle">订单列表、状态跟踪、处理管理</div>
            </div>
        </div>
        <iframe src="orders.html"></iframe>
    </div>

    <!-- 配送管理页面 -->
    <div class="page-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-truck"></i>
            </div>
            <div>
                <div class="section-title">配送管理</div>
                <div class="section-subtitle">配送员管理、配送区域设置</div>
            </div>
        </div>
        <iframe src="delivery.html"></iframe>
    </div>

    <!-- 财务管理页面 -->
    <div class="page-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-coins"></i>
            </div>
            <div>
                <div class="section-title">财务管理</div>
                <div class="section-subtitle">收益统计、结算管理</div>
            </div>
        </div>
        <iframe src="finance.html"></iframe>
    </div>

    <!-- 系统设置页面 -->
    <div class="page-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-cogs"></i>
            </div>
            <div>
                <div class="section-title">系统设置</div>
                <div class="section-subtitle">系统参数配置、权限管理</div>
            </div>
        </div>
        <iframe src="settings.html"></iframe>
    </div>

    <!-- 底部信息 -->
    <div style="background: rgba(255,255,255,0.1); color: white; text-align: center; padding: 20px; margin: 40px;">
        <p>&copy; 2024 校园外卖产品后台管理系统 | 版本 v2.1.0 | 技术支持：产品技术部</p>
    </div>
</body>
</html>
