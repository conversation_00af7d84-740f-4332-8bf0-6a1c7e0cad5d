<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家入驻系统原型</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f0f2f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .phone-frame {
            width: 393px;
            height: 852px;
            background: white;
            border-radius: 50px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            position: relative;
            margin: 0 auto;
            border: 12px solid #111;
            position: relative;
            transform: scale(0.9);
            transform-origin: top center;
        }
        .phone-frame::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 30px;
            background: #111;
            border-bottom-left-radius: 16px;
            border-bottom-right-radius: 16px;
            z-index: 10;
        }
        .screen {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 38px;
            overflow: hidden;
        }
        .screen-title {
            text-align: center;
            font-weight: bold;
            margin-top: 10px;
            margin-bottom: 5px;
            font-size: 16px;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #07C160 0%, #1AAD19 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            padding: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container mx-auto py-8">
        <h1 class="page-title">商家入驻系统原型图</h1>

        <div class="prototype-container">
            <!-- Welcome Screen -->
            <div>
                <div class="screen-title">欢迎页</div>
                <div class="phone-frame">
                    <iframe src="welcome.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Industry Selection -->
            <div>
                <div class="screen-title">行业选择</div>
                <div class="phone-frame">
                    <iframe src="industry-selection.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Basic Info -->
            <div>
                <div class="screen-title">基础信息</div>
                <div class="phone-frame">
                    <iframe src="basic-info.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Qualification Upload -->
            <div>
                <div class="screen-title">资质上传</div>
                <div class="phone-frame">
                    <iframe src="qualification-upload.html" class="screen"></iframe>
                </div>
            </div>

            <!-- ID Card Upload -->
            <div>
                <div class="screen-title">身份证上传</div>
                <div class="phone-frame">
                    <iframe src="id-card-upload.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Industry Config -->
            <div>
                <div class="screen-title">行业特性配置</div>
                <div class="phone-frame">
                    <iframe src="industry-config.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Product Upload -->
            <div>
                <div class="screen-title">商品上传</div>
                <div class="phone-frame">
                    <iframe src="product-upload.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Preview -->
            <div>
                <div class="screen-title">信息预览</div>
                <div class="phone-frame">
                    <iframe src="preview.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Verification -->
            <div>
                <div class="screen-title">审核</div>
                <div class="phone-frame">
                    <iframe src="verification.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Contract -->
            <div>
                <div class="screen-title">合同签署</div>
                <div class="phone-frame">
                    <iframe src="contract.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Payment Setup -->
            <div>
                <div class="screen-title">结算设置</div>
                <div class="phone-frame">
                    <iframe src="payment-setup.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Opening Preparation -->
            <div>
                <div class="screen-title">开业准备</div>
                <div class="phone-frame">
                    <iframe src="opening-prep.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Business Hours -->
            <div>
                <div class="screen-title">营业时间设置</div>
                <div class="phone-frame">
                    <iframe src="business-hours.html" class="screen"></iframe>
                </div>
            </div>

            <!-- POS Setup -->
            <div>
                <div class="screen-title">收银设备安装</div>
                <div class="phone-frame">
                    <iframe src="pos-setup.html" class="screen"></iframe>
                </div>
            </div>

            <!-- Staff Training -->
            <div>
                <div class="screen-title">店员培训</div>
                <div class="phone-frame">
                    <iframe src="staff-training.html" class="screen"></iframe>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
