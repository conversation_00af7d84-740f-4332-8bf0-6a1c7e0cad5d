<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            width: 1440px;
            margin: 0;
            padding: 0;
        }
        .merchant-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border-left: 4px solid #3b82f6;
        }
        .merchant-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-active { background: #d1fae5; color: #059669; }
        .status-pending { background: #fef3c7; color: #d97706; }
        .status-suspended { background: #fee2e2; color: #dc2626; }
        .status-reviewing { background: #dbeafe; color: #2563eb; }
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { transform: translateY(-1px); }
        .form-input, .form-select {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.2s;
            background: #fafbfc;
        }
        .form-input:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
            background: white;
        }
        .metric-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }
        .rating-stars {
            color: #fbbf24;
        }
        .merchant-avatar {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-right: 16px;
        }
        .progress-ring {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: conic-gradient(#3b82f6 0deg 252deg, #e5e7eb 252deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .progress-ring::before {
            content: '';
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        .progress-text {
            position: relative;
            z-index: 1;
            font-weight: bold;
            color: #3b82f6;
        }
        .tab-button {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        .tab-button.active {
            background: #3b82f6;
            color: white;
        }
        .tab-button:not(.active) {
            background: #f1f5f9;
            color: #64748b;
        }
        .tab-button:not(.active):hover {
            background: #e2e8f0;
            color: #475569;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-8">
        <!-- 页面标题和操作 -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">商家管理</h1>
                <p class="text-gray-500 mt-2">管理校园外卖平台商家信息、审核和状态</p>
            </div>
            <div class="flex gap-3">
                <button class="btn btn-success">
                    <i class="fas fa-plus mr-2"></i>添加商家
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-download mr-2"></i>导出数据
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-5 gap-6 mb-8">
            <div class="metric-card">
                <div class="text-3xl font-bold text-gray-900 mb-2">156</div>
                <div class="text-sm text-gray-500">总商家数</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-green-600 mb-2">89</div>
                <div class="text-sm text-gray-500">营业中</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-orange-600 mb-2">12</div>
                <div class="text-sm text-gray-500">待审核</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-blue-600 mb-2">8</div>
                <div class="text-sm text-gray-500">审核中</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-red-600 mb-2">5</div>
                <div class="text-sm text-gray-500">已暂停</div>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="flex gap-2 mb-6">
            <button class="tab-button active">全部商家</button>
            <button class="tab-button">待审核</button>
            <button class="tab-button">营业中</button>
            <button class="tab-button">已暂停</button>
            <button class="tab-button">异常商家</button>
        </div>

        <!-- 筛选区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="grid grid-cols-5 gap-4">
                <select class="form-select">
                    <option>全部状态</option>
                    <option>营业中</option>
                    <option>待审核</option>
                    <option>已暂停</option>
                </select>
                <select class="form-select">
                    <option>全部类型</option>
                    <option>餐饮美食</option>
                    <option>水果生鲜</option>
                    <option>便民超市</option>
                    <option>饮品咖啡</option>
                </select>
                <select class="form-select">
                    <option>全部区域</option>
                    <option>东区</option>
                    <option>西区</option>
                    <option>南区</option>
                    <option>北区</option>
                </select>
                <input type="text" placeholder="搜索商家名称..." class="form-input">
                <button class="btn btn-primary">
                    <i class="fas fa-search mr-2"></i>搜索
                </button>
            </div>
        </div>

        <!-- 商家列表 -->
        <div class="space-y-6">
            <!-- 商家1 -->
            <div class="merchant-card">
                <div class="flex items-start justify-between">
                    <div class="flex items-start">
                        <div class="merchant-avatar" style="background: linear-gradient(135deg, #ff6b6b, #ffa726);">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-2">
                                <h3 class="text-xl font-bold text-gray-900">麻辣香锅店</h3>
                                <span class="status-badge status-active">营业中</span>
                                <div class="flex items-center">
                                    <div class="rating-stars mr-1">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="text-sm text-gray-600">4.8分 (1,234评价)</span>
                                </div>
                            </div>
                            <div class="grid grid-cols-4 gap-6 mb-4">
                                <div>
                                    <div class="text-sm text-gray-500">负责人</div>
                                    <div class="font-semibold text-gray-900">张老板</div>
                                    <div class="text-sm text-gray-500">138****5678</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">商家类型</div>
                                    <div class="font-semibold text-gray-900">餐饮美食</div>
                                    <div class="text-sm text-gray-500">川菜 | 香锅</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">服务区域</div>
                                    <div class="font-semibold text-gray-900">东区、南区</div>
                                    <div class="text-sm text-gray-500">配送范围3公里</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">入驻时间</div>
                                    <div class="font-semibold text-gray-900">2023-03-15</div>
                                    <div class="text-sm text-gray-500">合作11个月</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-4 gap-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">156</div>
                                    <div class="text-sm text-gray-500">今日订单</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">¥4,680</div>
                                    <div class="text-sm text-gray-500">今日收益</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600">98.5%</div>
                                    <div class="text-sm text-gray-500">接单率</div>
                                </div>
                                <div class="text-center">
                                    <div class="progress-ring">
                                        <div class="progress-text">70%</div>
                                    </div>
                                    <div class="text-sm text-gray-500 mt-2">完成度</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <button class="btn btn-primary">详情</button>
                        <button class="btn btn-warning">编辑</button>
                        <button class="btn btn-success">消息</button>
                        <button class="btn btn-danger">暂停</button>
                    </div>
                </div>
            </div>

            <!-- 商家2 -->
            <div class="merchant-card">
                <div class="flex items-start justify-between">
                    <div class="flex items-start">
                        <div class="merchant-avatar" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <i class="fas fa-apple-alt"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-2">
                                <h3 class="text-xl font-bold text-gray-900">新鲜水果店</h3>
                                <span class="status-badge status-pending">待审核</span>
                                <div class="flex items-center">
                                    <div class="rating-stars mr-1">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                    <span class="text-sm text-gray-600">4.6分 (856评价)</span>
                                </div>
                            </div>
                            <div class="grid grid-cols-4 gap-6 mb-4">
                                <div>
                                    <div class="text-sm text-gray-500">负责人</div>
                                    <div class="font-semibold text-gray-900">李经理</div>
                                    <div class="text-sm text-gray-500">139****1234</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">商家类型</div>
                                    <div class="font-semibold text-gray-900">水果生鲜</div>
                                    <div class="text-sm text-gray-500">水果 | 蔬菜</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">申请区域</div>
                                    <div class="font-semibold text-gray-900">西区、北区</div>
                                    <div class="text-sm text-gray-500">配送范围2公里</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">申请时间</div>
                                    <div class="font-semibold text-gray-900">2024-02-20</div>
                                    <div class="text-sm text-gray-500">待审核3天</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-4 gap-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">0</div>
                                    <div class="text-sm text-gray-500">今日订单</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">¥0</div>
                                    <div class="text-sm text-gray-500">今日收益</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600">-</div>
                                    <div class="text-sm text-gray-500">接单率</div>
                                </div>
                                <div class="text-center">
                                    <div class="progress-ring" style="background: conic-gradient(#f59e0b 0deg 180deg, #e5e7eb 180deg 360deg);">
                                        <div class="progress-text" style="color: #f59e0b;">50%</div>
                                    </div>
                                    <div class="text-sm text-gray-500 mt-2">资料完成度</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <button class="btn btn-success">审核通过</button>
                        <button class="btn btn-danger">审核拒绝</button>
                        <button class="btn btn-primary">查看资料</button>
                        <button class="btn btn-warning">要求补充</button>
                    </div>
                </div>
            </div>

            <!-- 商家3 -->
            <div class="merchant-card">
                <div class="flex items-start justify-between">
                    <div class="flex items-start">
                        <div class="merchant-avatar" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-shopping-basket"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-2">
                                <h3 class="text-xl font-bold text-gray-900">便民超市</h3>
                                <span class="status-badge status-suspended">已暂停</span>
                                <div class="flex items-center">
                                    <div class="rating-stars mr-1">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                    <span class="text-sm text-gray-600">3.8分 (456评价)</span>
                                </div>
                            </div>
                            <div class="grid grid-cols-4 gap-6 mb-4">
                                <div>
                                    <div class="text-sm text-gray-500">负责人</div>
                                    <div class="font-semibold text-gray-900">王店长</div>
                                    <div class="text-sm text-gray-500">137****9876</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">商家类型</div>
                                    <div class="font-semibold text-gray-900">便民超市</div>
                                    <div class="text-sm text-gray-500">日用品 | 零食</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">服务区域</div>
                                    <div class="font-semibold text-gray-900">南区</div>
                                    <div class="text-sm text-gray-500">配送范围1.5公里</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">暂停时间</div>
                                    <div class="font-semibold text-gray-900">2024-02-18</div>
                                    <div class="text-sm text-red-500">违规暂停</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-4 gap-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-400">0</div>
                                    <div class="text-sm text-gray-500">今日订单</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-400">¥0</div>
                                    <div class="text-sm text-gray-500">今日收益</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-400">-</div>
                                    <div class="text-sm text-gray-500">接单率</div>
                                </div>
                                <div class="text-center">
                                    <div class="progress-ring" style="background: conic-gradient(#ef4444 0deg 72deg, #e5e7eb 72deg 360deg);">
                                        <div class="progress-text" style="color: #ef4444;">20%</div>
                                    </div>
                                    <div class="text-sm text-gray-500 mt-2">信誉度</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <button class="btn btn-success">恢复营业</button>
                        <button class="btn btn-primary">查看详情</button>
                        <button class="btn btn-warning">发送警告</button>
                        <button class="btn btn-danger">永久封禁</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="mt-8 flex justify-between items-center">
            <div class="text-sm text-gray-500">
                显示 1-3 条，共 156 条记录
            </div>
            <div class="flex gap-2">
                <button class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">上一页</button>
                <button class="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm">1</button>
                <button class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">2</button>
                <button class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">3</button>
                <button class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">下一页</button>
            </div>
        </div>
    </div>
</body>
</html>
