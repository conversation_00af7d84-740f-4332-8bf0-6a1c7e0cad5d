<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>用户主页</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .profile-header {
            padding: 44px 16px 16px;
            background: #fff;
            text-align: center;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 12px;
        }
        
        .profile-username {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .profile-bio {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .profile-stats {
            display: flex;
            justify-content: center;
            gap: 32px;
            margin-bottom: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-weight: 600;
            font-size: 18px;
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .profile-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-bottom: 16px;
        }
        
        .action-btn {
            padding: 8px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .primary-btn {
            background: var(--primary-color);
            color: #fff;
            border: none;
        }
        
        .secondary-btn {
            background: #fff;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .content-tabs {
            background: #fff;
            display: flex;
            margin-bottom: 1px;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            color: var(--text-secondary);
            font-size: 14px;
            position: relative;
        }
        
        .tab-item--active {
            color: var(--primary-color);
        }
        
        .tab-item--active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 2px;
            background: var(--primary-color);
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1px;
            background: #E8E8E8;
        }
        
        .video-item {
            aspect-ratio: 9/16;
            background: #000;
            position: relative;
        }
        
        .video-item__info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 4px;
            background: rgba(0,0,0,0.5);
            color: #fff;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <i class="fas fa-arrow-left" onclick="history.back()"></i>
        <div class="status-bar__title">用户主页</div>
        <div class="status-bar__icons">
            <i class="fas fa-ellipsis-v"></i>
        </div>
    </div>

    <div class="profile-header">
        <img src="https://via.placeholder.com/80" alt="用户头像" class="profile-avatar">
        <h1 class="profile-username">美食达人</h1>
        <p class="profile-bio">分享美食，传递快乐 🍳</p>
        
        <div class="profile-stats">
            <div class="stat-item">
                <div class="stat-number">128</div>
                <div class="stat-label">作品</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">12.8w</div>
                <div class="stat-label">粉丝</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">256</div>
                <div class="stat-label">关注</div>
            </div>
        </div>
        
        <div class="profile-actions">
            <button class="action-btn primary-btn btn-hover">关注</button>
            <button class="action-btn secondary-btn btn-hover">私信</button>
        </div>
    </div>

    <div class="content-tabs">
        <div class="tab-item tab-item--active">
            <i class="fas fa-grip-vertical"></i> 作品
        </div>
        <div class="tab-item">
            <i class="fas fa-heart"></i> 喜欢
        </div>
        <div class="tab-item">
            <i class="fas fa-bookmark"></i> 收藏
        </div>
    </div>

    <div class="video-grid">
        <!-- 视频网格 -->
        <div class="video-item">
            <img src="https://via.placeholder.com/180x320" alt="视频封面">
            <div class="video-item__info">
                <i class="fas fa-play"></i> 10.2w
            </div>
        </div>
        <!-- 更多视频项... -->
    </div>

    <script src="scripts/app.js"></script>
</body>
</html>
