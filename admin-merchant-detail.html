<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家详情</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }
        
        .admin-layout {
            display: flex;
            height: calc(100vh - 44px);
        }
        
        .sidebar {
            width: 80px;
            background-color: #001529;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 0;
            overflow-y: auto;
        }
        
        .sidebar-logo {
            width: 40px;
            height: 40px;
            background-color: #1890ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            color: white;
            font-size: 20px;
        }
        
        .sidebar-menu {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        
        .menu-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            color: rgba(255, 255, 255, 0.65);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: white;
        }
        
        .menu-item.active {
            color: white;
            background-color: #1890ff;
        }
        
        .menu-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-left: 16px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .header-icon:hover {
            color: #1890ff;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 16px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .page-actions {
            display: flex;
            align-items: center;
        }
        
        .action-button {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin-left: 8px;
        }
        
        .action-button i {
            margin-right: 8px;
        }
        
        .action-primary {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        
        .action-primary:hover {
            background-color: #40a9ff;
        }
        
        .action-default {
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .action-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .action-danger {
            background-color: white;
            color: #ff4d4f;
            border: 1px solid #ff4d4f;
        }
        
        .action-danger:hover {
            background-color: #ff4d4f;
            color: white;
        }
        
        .detail-layout {
            display: flex;
            gap: 24px;
        }
        
        .detail-main {
            flex: 3;
        }
        
        .detail-sidebar {
            flex: 1;
        }
        
        .detail-card {
            background-color: white;
            border-radius: 8px;
            margin-bottom: 24px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .card-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .card-actions {
            display: flex;
            align-items: center;
        }
        
        .card-action {
            color: #1890ff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 16px;
        }
        
        .card-action:hover {
            color: #40a9ff;
        }
        
        .card-content {
            padding: 20px;
        }
        
        .merchant-profile {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .merchant-logo {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: #999;
            font-size: 32px;
        }
        
        .merchant-info {
            flex: 1;
        }
        
        .merchant-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .merchant-meta {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .merchant-id {
            font-size: 14px;
            color: #666;
            margin-right: 16px;
        }
        
        .merchant-category {
            font-size: 12px;
            padding: 2px 8px;
            background-color: #e6f7ff;
            color: #1890ff;
            border-radius: 4px;
        }
        
        .merchant-contact {
            font-size: 14px;
            color: #666;
        }
        
        .info-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .document-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .document-item {
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .document-image {
            width: 100%;
            height: 120px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 32px;
        }
        
        .document-info {
            padding: 12px;
            border-top: 1px solid #f0f0f0;
        }
        
        .document-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .document-meta {
            font-size: 12px;
            color: #999;
        }
        
        .timeline {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        
        .timeline-item {
            position: relative;
            padding-bottom: 20px;
            padding-left: 20px;
        }
        
        .timeline-item::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #1890ff;
        }
        
        .timeline-item::after {
            content: "";
            position: absolute;
            left: 3px;
            top: 8px;
            width: 2px;
            height: calc(100% - 8px);
            background-color: #f0f0f0;
        }
        
        .timeline-item:last-child {
            padding-bottom: 0;
        }
        
        .timeline-item:last-child::after {
            display: none;
        }
        
        .timeline-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #999;
        }
        
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-logo">
                <i class="fas fa-store"></i>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user-check menu-icon"></i>
                    <span>审核</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span>统计</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>设置</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <i class="fas fa-bars" style="font-size: 18px; color: #666; cursor: pointer;"></i>
                    <div class="header-title">商家详情</div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">5</div>
                    </div>
                    <div class="user-avatar">
                        <span>管</span>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <div class="page-title">品味小厨</div>
                    <div class="page-actions">
                        <button class="action-button action-default">
                            <i class="fas fa-edit"></i>
                            <span>编辑</span>
                        </button>
                        <button class="action-button action-primary">
                            <i class="fas fa-check"></i>
                            <span>审核通过</span>
                        </button>
                        <button class="action-button action-danger">
                            <i class="fas fa-times"></i>
                            <span>拒绝</span>
                        </button>
                    </div>
                </div>
                
                <div class="detail-layout">
                    <div class="detail-main">
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">基本信息</div>
                                <div class="card-actions">
                                    <span class="card-action">编辑</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="merchant-profile">
                                    <div class="merchant-logo">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <div class="merchant-info">
                                        <div class="merchant-name">品味小厨</div>
                                        <div class="merchant-meta">
                                            <div class="merchant-id">ID: 10001</div>
                                            <div class="merchant-category">餐饮</div>
                                        </div>
                                        <div class="merchant-contact">联系人: 张先生 (13812345678)</div>
                                    </div>
                                </div>
                                
                                <div class="info-list">
                                    <div class="info-item">
                                        <div class="info-label">商家类型</div>
                                        <div class="info-value">餐饮 - 中式快餐</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">营业状态</div>
                                        <div class="info-value"><span class="status-tag status-pending">待审核</span></div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">入驻时间</div>
                                        <div class="info-value">2023-05-18 14:23</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">营业地址</div>
                                        <div class="info-value">北京市朝阳区建国路88号</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">营业时间</div>
                                        <div class="info-value">周一至周日 10:00-22:00</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">联系电话</div>
                                        <div class="info-value">010-88889999</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">资质证明</div>
                                <div class="card-actions">
                                    <span class="card-action">查看原图</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="document-list">
                                    <div class="document-item">
                                        <div class="document-image">
                                            <i class="fas fa-file-image"></i>
                                        </div>
                                        <div class="document-info">
                                            <div class="document-name">营业执照</div>
                                            <div class="document-meta">上传时间: 2023-05-18 14:10</div>
                                        </div>
                                    </div>
                                    <div class="document-item">
                                        <div class="document-image">
                                            <i class="fas fa-file-image"></i>
                                        </div>
                                        <div class="document-info">
                                            <div class="document-name">食品经营许可证</div>
                                            <div class="document-meta">上传时间: 2023-05-18 14:12</div>
                                        </div>
                                    </div>
                                    <div class="document-item">
                                        <div class="document-image">
                                            <i class="fas fa-file-image"></i>
                                        </div>
                                        <div class="document-info">
                                            <div class="document-name">法人身份证正面</div>
                                            <div class="document-meta">上传时间: 2023-05-18 14:15</div>
                                        </div>
                                    </div>
                                    <div class="document-item">
                                        <div class="document-image">
                                            <i class="fas fa-file-image"></i>
                                        </div>
                                        <div class="document-info">
                                            <div class="document-name">法人身份证反面</div>
                                            <div class="document-meta">上传时间: 2023-05-18 14:15</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">商品信息</div>
                                <div class="card-actions">
                                    <span class="card-action">查看全部</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <table class="data-table w-full">
                                    <thead>
                                        <tr>
                                            <th>商品名称</th>
                                            <th>分类</th>
                                            <th>价格</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>宫保鸡丁</td>
                                            <td>热菜</td>
                                            <td>¥38</td>
                                            <td><span class="status-tag status-approved">上架中</span></td>
                                        </tr>
                                        <tr>
                                            <td>水煮鱼</td>
                                            <td>热菜</td>
                                            <td>¥58</td>
                                            <td><span class="status-tag status-approved">上架中</span></td>
                                        </tr>
                                        <tr>
                                            <td>麻婆豆腐</td>
                                            <td>热菜</td>
                                            <td>¥28</td>
                                            <td><span class="status-tag status-approved">上架中</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-sidebar">
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">审核状态</div>
                            </div>
                            <div class="card-content">
                                <div class="text-center mb-4">
                                    <span class="status-tag status-pending text-base px-4 py-1">待审核</span>
                                </div>
                                
                                <ul class="timeline">
                                    <li class="timeline-item">
                                        <div class="timeline-title">提交入驻申请</div>
                                        <div class="timeline-time">2023-05-18 14:23</div>
                                    </li>
                                    <li class="timeline-item">
                                        <div class="timeline-title">上传资质证明</div>
                                        <div class="timeline-time">2023-05-18 14:15</div>
                                    </li>
                                    <li class="timeline-item">
                                        <div class="timeline-title">填写基本信息</div>
                                        <div class="timeline-time">2023-05-18 14:05</div>
                                    </li>
                                    <li class="timeline-item">
                                        <div class="timeline-title">注册账号</div>
                                        <div class="timeline-time">2023-05-18 14:00</div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">操作记录</div>
                            </div>
                            <div class="card-content">
                                <ul class="timeline">
                                    <li class="timeline-item">
                                        <div class="timeline-title">系统自动分配审核</div>
                                        <div class="timeline-time">2023-05-18 14:25</div>
                                    </li>
                                    <li class="timeline-item">
                                        <div class="timeline-title">提交入驻申请</div>
                                        <div class="timeline-time">2023-05-18 14:23</div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">审核意见</div>
                            </div>
                            <div class="card-content">
                                <div class="form-group mb-4">
                                    <label class="form-label block mb-2 text-sm text-gray-700">审核结果</label>
                                    <select class="form-select w-full p-2 border border-gray-300 rounded">
                                        <option value="1">通过</option>
                                        <option value="2">拒绝</option>
                                        <option value="3">需补充资料</option>
                                    </select>
                                </div>
                                
                                <div class="form-group mb-4">
                                    <label class="form-label block mb-2 text-sm text-gray-700">审核意见</label>
                                    <textarea class="form-input w-full p-2 border border-gray-300 rounded" rows="4" placeholder="请输入审核意见"></textarea>
                                </div>
                                
                                <button class="action-button action-primary w-full justify-center">
                                    <span>提交审核</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
