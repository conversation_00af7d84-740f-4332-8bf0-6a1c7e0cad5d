<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>店员培训</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }

        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }

        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }

        .wechat-nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
        }

        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Progress Bar */
        .progress-container {
            padding: 12px 16px;
            background-color: #ffffff;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }

        .progress-bar {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background-color: #07C160;
            width: 100%;
        }

        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px - 57px);
            padding: 16px;
            background-color: #f6f6f6;
            overflow-y: auto;
        }

        .content-description {
            font-size: 13px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.4;
        }

        /* Training Card */
        .training-card {
            background-color: #ffffff;
            border-radius: 8px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            overflow: hidden;
        }

        .training-header {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .training-title {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .training-subtitle {
            font-size: 12px;
            color: #666;
        }

        .training-content {
            padding: 16px;
        }

        /* Video Card */
        .video-card {
            position: relative;
            width: 100%;
            height: 180px;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .video-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.7;
        }

        .video-play {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 48px;
            height: 48px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .video-duration {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
        }

        .video-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-top: 8px;
            margin-bottom: 4px;
        }

        .video-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        /* Staff List */
        .staff-list {
            margin-top: 12px;
        }

        .staff-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .staff-item:last-child {
            border-bottom: none;
        }

        .staff-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 16px;
            margin-right: 12px;
        }

        .staff-info {
            flex: 1;
        }

        .staff-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .staff-role {
            font-size: 12px;
            color: #666;
        }

        .staff-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            background-color: #f0f0f0;
            color: #666;
        }

        .staff-status.completed {
            background-color: #f0fdf4;
            color: #07C160;
        }

        .staff-status.pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }

        .add-staff {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 0;
            color: #07C160;
            font-size: 14px;
            cursor: pointer;
        }

        .add-staff i {
            margin-right: 4px;
        }

        /* Quiz Card */
        .quiz-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
        }

        .quiz-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .quiz-option {
            display: flex;
            align-items: flex-start;
            padding: 8px 0;
        }

        .quiz-radio {
            width: 16px;
            height: 16px;
            border: 2px solid #d9d9d9;
            border-radius: 50%;
            margin-right: 8px;
            margin-top: 2px;
            position: relative;
            flex-shrink: 0;
        }

        .quiz-option.selected .quiz-radio {
            border-color: #07C160;
        }

        .quiz-option.selected .quiz-radio::after {
            content: "";
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: #07C160;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .quiz-text {
            font-size: 13px;
            color: #333;
            line-height: 1.4;
        }

        /* Bottom Button */
        .bottom-button {
            padding: 12px 16px;
            background-color: #ffffff;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 50px;
            width: 100%;
            box-sizing: border-box;
        }

        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
        }

        /* Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }

        .tab-item.active {
            color: #07C160;
        }

        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="wechat-nav-title">店员培训</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-info">
            <span>入驻进度</span>
            <span>10/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <p class="content-description">为确保您的店铺顺利运营，请完成以下培训内容并邀请店员学习</p>

        <div class="training-card">
            <div class="training-header">
                <h3 class="training-title">培训视频</h3>
                <p class="training-subtitle">观看以下视频了解平台操作流程</p>
            </div>
            <div class="training-content">
                <div class="video-card">
                    <img src="https://images.unsplash.com/photo-1556742031-c6961e8560b0?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1350&q=80" class="video-thumbnail">
                    <div class="video-play">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="video-duration">05:32</div>
                </div>
                <h4 class="video-title">接单流程与订单处理</h4>
                <p class="video-desc">学习如何接收新订单、确认订单和处理订单状态变更</p>

                <div class="video-card">
                    <img src="https://images.unsplash.com/photo-1556741533-6e6a62bd8b49?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1350&q=80" class="video-thumbnail">
                    <div class="video-play">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="video-duration">04:18</div>
                </div>
                <h4 class="video-title">收银设备使用指南</h4>
                <p class="video-desc">了解如何使用收银设备完成扫码支付和打印小票</p>
            </div>
        </div>

        <div class="training-card">
            <div class="training-header">
                <h3 class="training-title">店员管理</h3>
                <p class="training-subtitle">邀请店员并跟踪培训进度</p>
            </div>
            <div class="training-content">
                <div class="staff-list">
                    <div class="staff-item">
                        <div class="staff-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="staff-info">
                            <div class="staff-name">张经理</div>
                            <div class="staff-role">店长</div>
                        </div>
                        <div class="staff-status completed">已完成</div>
                    </div>

                    <div class="staff-item">
                        <div class="staff-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="staff-info">
                            <div class="staff-name">李小姐</div>
                            <div class="staff-role">收银员</div>
                        </div>
                        <div class="staff-status pending">进行中</div>
                    </div>

                    <div class="staff-item">
                        <div class="staff-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="staff-info">
                            <div class="staff-name">王先生</div>
                            <div class="staff-role">服务员</div>
                        </div>
                        <div class="staff-status pending">未开始</div>
                    </div>

                    <div class="add-staff">
                        <i class="fas fa-plus-circle"></i>
                        <span>添加店员</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="training-card">
            <div class="training-header">
                <h3 class="training-title">知识测验</h3>
                <p class="training-subtitle">完成以下测验检验学习成果</p>
            </div>
            <div class="training-content">
                <div class="quiz-card">
                    <h4 class="quiz-title">1. 收到新订单后，应该如何操作？</h4>
                    <div class="quiz-option selected">
                        <div class="quiz-radio"></div>
                        <div class="quiz-text">在收银设备上确认订单，然后开始准备商品</div>
                    </div>
                    <div class="quiz-option">
                        <div class="quiz-radio"></div>
                        <div class="quiz-text">直接开始准备商品，不需要在系统中确认</div>
                    </div>
                    <div class="quiz-option">
                        <div class="quiz-radio"></div>
                        <div class="quiz-text">联系客户确认订单信息后再操作</div>
                    </div>
                </div>

                <div class="quiz-card">
                    <h4 class="quiz-title">2. 顾客要求退款时，正确的处理流程是？</h4>
                    <div class="quiz-option">
                        <div class="quiz-radio"></div>
                        <div class="quiz-text">直接给顾客现金退款</div>
                    </div>
                    <div class="quiz-option selected">
                        <div class="quiz-radio"></div>
                        <div class="quiz-text">在系统中申请退款，等待平台审核通过</div>
                    </div>
                    <div class="quiz-option">
                        <div class="quiz-radio"></div>
                        <div class="quiz-text">拒绝顾客的退款请求</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="bottom-button">
        <button class="btn-primary">完成培训</button>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
