<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础信息 - 商家入驻</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f6f6f6;
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        /* iOS Status Bar */
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }

        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }

        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }

        .wechat-nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
        }

        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Progress Bar */
        .progress-container {
            padding: 12px 16px;
            background-color: #ffffff;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }

        .progress-bar {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background-color: #07C160;
            width: 20%;
        }

        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px - 57px);
            padding: 16px;
            background-color: #f6f6f6;
            display: flex;
            flex-direction: column;
        }

        .content-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        /* Form Styles */
        .form-card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            color: #333;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #EBEBEB;
            border-radius: 4px;
            font-size: 13px;
            background-color: #fff;
            box-sizing: border-box;
        }

        .form-input:focus {
            border-color: #07C160;
            outline: none;
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #EBEBEB;
            border-radius: 4px;
            font-size: 14px;
            background-color: #fff;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            box-sizing: border-box;
        }

        .form-select:focus {
            border-color: #07C160;
            outline: none;
        }

        .form-textarea {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #EBEBEB;
            border-radius: 4px;
            font-size: 13px;
            background-color: #fff;
            resize: none;
            min-height: 60px;
            box-sizing: border-box;
        }

        .form-textarea:focus {
            border-color: #07C160;
            outline: none;
        }

        .required {
            color: #FF4D4F;
            margin-left: 4px;
        }

        .time-input-group {
            display: flex;
            align-items: center;
        }

        .time-input {
            flex: 1;
        }

        .time-separator {
            padding: 0 8px;
            color: #999;
        }

        .map-container {
            height: 120px;
            background-image: url('https://api.map.baidu.com/staticimage/v2?ak=E4805d16520de693a3fe707cdc962045&width=400&height=300&center=116.403874,39.914889&zoom=14');
            background-size: cover;
            background-position: center;
            border-radius: 4px;
            position: relative;
            margin-bottom: 8px;
        }

        .map-pin {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -100%);
            color: #FF4D4F;
            font-size: 24px;
        }

        .map-select-btn {
            position: absolute;
            bottom: 12px;
            right: 12px;
            background-color: #fff;
            color: #333;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        /* Bottom Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }

        .tab-item.active {
            color: #07C160;
        }

        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }

        /* Bottom Button */
        .bottom-button {
            padding: 12px 16px;
            background-color: #ffffff;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 50px;
            width: 100%;
            box-sizing: border-box;
        }

        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="wechat-nav-title">基础信息</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-info">
            <span>入驻进度</span>
            <span>2/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <p class="content-description">请填写您的商家基本信息，带<span class="required">*</span>为必填项</p>

        <div class="form-card">
            <div class="form-group">
                <label class="form-label">商家名称<span class="required">*</span></label>
                <input type="text" class="form-input" placeholder="请输入商家名称">
            </div>

            <div class="form-group">
                <label class="form-label">联系人<span class="required">*</span></label>
                <input type="text" class="form-input" placeholder="请输入联系人姓名">
            </div>

            <div class="form-group">
                <label class="form-label">联系电话<span class="required">*</span></label>
                <input type="tel" class="form-input" placeholder="请输入联系电话">
            </div>

            <div class="form-group">
                <label class="form-label">电子邮箱</label>
                <input type="email" class="form-input" placeholder="请输入电子邮箱">
            </div>
        </div>

        <div class="form-card">
            <div class="form-group">
                <label class="form-label">经营类型<span class="required">*</span></label>
                <select class="form-select">
                    <option value="" disabled selected>请选择经营类型</option>
                    <option value="1">个体工商户</option>
                    <option value="2">有限责任公司</option>
                    <option value="3">股份有限公司</option>
                    <option value="4">其他</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">营业时间<span class="required">*</span></label>
                <div class="time-input-group">
                    <input type="text" class="form-input time-input" placeholder="开始时间" value="09:00">
                    <span class="time-separator">至</span>
                    <input type="text" class="form-input time-input" placeholder="结束时间" value="21:00">
                </div>
            </div>
        </div>

        <div class="form-card">
            <div class="form-group">
                <label class="form-label">店铺地址<span class="required">*</span></label>
                <div class="map-container">
                    <i class="fas fa-map-marker-alt map-pin"></i>
                    <button class="map-select-btn">
                        <i class="fas fa-crosshairs mr-1"></i> 选择位置
                    </button>
                </div>
                <input type="text" class="form-input" placeholder="请输入详细地址">
            </div>
        </div>

        <div class="form-card">
            <div class="form-group">
                <label class="form-label">店铺简介</label>
                <textarea class="form-textarea" placeholder="请简要描述您的店铺特色和服务"></textarea>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="bottom-button">
        <button class="btn-primary">保存并继续</button>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
