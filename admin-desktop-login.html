<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            height: 100vh;
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            display: flex;
            width: 1440px;
            height: 900px;
            background-color: white;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .login-banner {
            flex: 1;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 40px;
        }
        
        .banner-logo {
            width: 120px;
            height: 120px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            font-size: 60px;
        }
        
        .banner-title {
            font-size: 36px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .banner-subtitle {
            font-size: 18px;
            opacity: 0.8;
            text-align: center;
            max-width: 400px;
            line-height: 1.6;
        }
        
        .banner-features {
            margin-top: 60px;
            width: 100%;
            max-width: 400px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 16px;
        }
        
        .feature-text {
            font-size: 16px;
        }
        
        .login-form-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
        }
        
        .login-header {
            margin-bottom: 40px;
            text-align: center;
        }
        
        .login-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .login-subtitle {
            font-size: 16px;
            color: #666;
        }
        
        .login-form {
            width: 100%;
            max-width: 360px;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-input-prefix {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #bfbfbf;
        }
        
        .input-with-icon {
            padding-left: 40px;
        }
        
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
        }
        
        .checkbox {
            margin-right: 8px;
        }
        
        .forgot-password {
            color: #1890ff;
            font-size: 14px;
            text-decoration: none;
        }
        
        .login-button {
            width: 100%;
            padding: 12px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .login-button:hover {
            background-color: #40a9ff;
        }
        
        .login-button:active {
            background-color: #096dd9;
        }
        
        .other-login {
            margin-top: 24px;
            text-align: center;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 16px 0;
        }
        
        .divider-line {
            flex: 1;
            height: 1px;
            background-color: #e8e8e8;
        }
        
        .divider-text {
            padding: 0 16px;
            color: #bfbfbf;
            font-size: 14px;
        }
        
        .social-login {
            display: flex;
            justify-content: center;
            margin-top: 16px;
        }
        
        .social-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 8px;
            color: #333;
            font-size: 18px;
            transition: all 0.3s;
        }
        
        .social-icon:hover {
            background-color: #e8e8e8;
        }
        
        .register-link {
            margin-top: 24px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        
        .register-link a {
            color: #1890ff;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-banner">
            <div class="banner-logo">
                <i class="fas fa-store"></i>
            </div>
            <h1 class="banner-title">商家入驻系统管理后台</h1>
            <p class="banner-subtitle">高效管理商家入驻、审核和运营的一站式平台</p>
            
            <div class="banner-features">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="feature-text">高效的商家审核流程</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="feature-text">全面的数据分析报表</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="feature-text">灵活的系统配置选项</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-text">安全的权限管理机制</div>
                </div>
            </div>
        </div>
        
        <div class="login-form-container">
            <div class="login-header">
                <h2 class="login-title">欢迎登录</h2>
                <p class="login-subtitle">请使用您的管理员账号登录系统</p>
            </div>
            
            <div class="login-form">
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <div class="form-input-prefix">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-input input-with-icon" placeholder="请输入管理员用户名">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <div class="form-input-prefix">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-input input-with-icon" placeholder="请输入密码">
                    </div>
                </div>
                
                <div class="remember-forgot">
                    <div class="remember-me">
                        <input type="checkbox" id="remember" class="checkbox">
                        <label for="remember" class="text-sm text-gray-600">记住我</label>
                    </div>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
                
                <button class="login-button">登录</button>
                
                <div class="other-login">
                    <div class="divider">
                        <div class="divider-line"></div>
                        <span class="divider-text">其他登录方式</span>
                        <div class="divider-line"></div>
                    </div>
                    
                    <div class="social-login">
                        <div class="social-icon">
                            <i class="fab fa-weixin" style="color: #07C160;"></i>
                        </div>
                        <div class="social-icon">
                            <i class="fas fa-mobile-alt" style="color: #1890ff;"></i>
                        </div>
                        <div class="social-icon">
                            <i class="fas fa-qrcode" style="color: #333;"></i>
                        </div>
                    </div>
                </div>
                
                <div class="register-link">
                    还没有账号？<a href="#">联系系统管理员</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
