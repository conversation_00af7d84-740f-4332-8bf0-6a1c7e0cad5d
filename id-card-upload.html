<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份证上传 - 商家入驻</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f6f6f6;
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        /* iOS Status Bar */
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }

        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }

        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }

        .wechat-nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
        }

        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Progress Bar */
        .progress-container {
            padding: 12px 16px;
            background-color: #ffffff;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }

        .progress-bar {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background-color: #07C160;
            width: 30%;
        }

        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px - 57px);
            padding: 16px;
            background-color: #f6f6f6;
            display: flex;
            flex-direction: column;
        }

        .content-description {
            font-size: 13px;
            color: #666;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        /* ID Card Card */
        .id-card-card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .id-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .id-card-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        .required-tag {
            background-color: #FFF1F0;
            color: #FF4D4F;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
        }

        .id-card-desc {
            font-size: 11px;
            color: #999;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        /* Upload Box */
        .upload-box {
            border: 1px dashed #DEDEDE;
            border-radius: 4px;
            background-color: #FAFAFA;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            height: 140px;
            margin-bottom: 10px;
        }

        .upload-box:active {
            background-color: #F5F5F5;
            border-color: #CCCCCC;
        }

        .upload-icon {
            font-size: 20px;
            color: #BFBFBF;
            margin-bottom: 6px;
        }

        .upload-text {
            font-size: 13px;
            color: #999;
        }

        .upload-hint {
            font-size: 11px;
            color: #BFBFBF;
            margin-top: 3px;
            text-align: center;
        }

        /* Uploaded Image */
        .uploaded-container {
            position: relative;
            margin-bottom: 8px;
        }

        .uploaded-image {
            width: 100%;
            height: 140px;
            object-fit: cover;
            border-radius: 4px;
        }

        .image-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }

        .image-name {
            font-size: 12px;
            color: #666;
        }

        .image-buttons {
            display: flex;
            align-items: center;
        }

        .image-button {
            background: none;
            border: none;
            font-size: 12px;
            padding: 0;
            margin-left: 12px;
            cursor: pointer;
        }

        .image-button.edit {
            color: #1890FF;
        }

        .image-button.delete {
            color: #FF4D4F;
        }

        /* ID Card Example */
        .id-card-example {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .example-image {
            width: 80px;
            height: 50px;
            background-color: #f0f0f0;
            border-radius: 4px;
            margin-right: 10px;
            background-size: cover;
            background-position: center;
        }

        .example-text {
            font-size: 11px;
            color: #999;
            flex: 1;
        }

        /* Bottom Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }

        .tab-item.active {
            color: #07C160;
        }

        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }

        /* Bottom Button */
        .bottom-button {
            padding: 12px 16px;
            background-color: #ffffff;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 50px;
            width: 100%;
            box-sizing: border-box;
        }

        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="wechat-nav-title">法人身份证上传</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-info">
            <span>入驻进度</span>
            <span>3/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <p class="content-description">请上传法人身份证正反面照片，确保图片清晰可见，信息完整</p>

        <div class="id-card-card">
            <div class="id-card-header">
                <span class="id-card-title">身份证正面</span>
                <span class="required-tag">必传</span>
            </div>
            <p class="id-card-desc">请上传身份证人像面，确保照片清晰，信息完整</p>

            <div class="id-card-example">
                <div class="example-image" style="background-image: url('https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2F1114%2F0G320120Z3%2F200G3120Z3-1-1200.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1659518345&t=f2d8f3b9e3b354fd7e1e333928427d7c')"></div>
                <div class="example-text">示例：身份证正面应包含头像、姓名、身份证号码等信息</div>
            </div>

            <div class="uploaded-container">
                <img src="https://img.zcool.cn/community/01a9a25af3dd3ca801216518a5c899.jpg@1280w_1l_2o_100sh.jpg" class="uploaded-image">
                <div class="image-actions">
                    <span class="image-name">id_card_front.jpg</span>
                    <div class="image-buttons">
                        <button class="image-button edit">重新上传</button>
                        <button class="image-button delete">删除</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="id-card-card">
            <div class="id-card-header">
                <span class="id-card-title">身份证反面</span>
                <span class="required-tag">必传</span>
            </div>
            <p class="id-card-desc">请上传身份证国徽面，确保照片清晰，信息完整</p>

            <div class="id-card-example">
                <div class="example-image" style="background-image: url('https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2F1114%2F0G320120Z3%2F200G3120Z3-2-1200.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1659518345&t=3b8e1d0c5b20969bae5f0050e737add5')"></div>
                <div class="example-text">示例：身份证反面应包含签发机关、有效期限等信息</div>
            </div>

            <div class="upload-box">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <div class="upload-text">点击上传图片</div>
                <div class="upload-hint">支持JPG、PNG格式，大小不超过5MB</div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="bottom-button">
        <button class="btn-primary">保存并返回</button>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
