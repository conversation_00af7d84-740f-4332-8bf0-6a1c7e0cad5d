<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审核</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
        }
        .status-bar::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 24px;
            background-color: #000;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .progress-bar {
            height: 4px;
            background-color: #e5e5e5;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #07c160;
            width: 70%;
        }
        .verification-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            margin-bottom: 16px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #d97706;
        }
        .status-approved {
            background-color: #d1fae5;
            color: #059669;
        }
        .status-rejected {
            background-color: #fee2e2;
            color: #dc2626;
        }
        .timeline {
            position: relative;
            padding-left: 28px;
        }
        .timeline::before {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 9px;
            width: 2px;
            background-color: #e5e5e5;
        }
        .timeline-item {
            position: relative;
            padding-bottom: 15px;
        }
        .timeline-item:last-child {
            padding-bottom: 0;
        }
        .timeline-dot {
            position: absolute;
            left: -28px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 9px;
            color: #9ca3af;
        }
        .timeline-dot.active {
            border-color: #07c160;
            color: #07c160;
        }
        .timeline-dot.completed {
            border-color: #07c160;
            background-color: #07c160;
            color: white;
        }
        .timeline-content {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 10px;
        }
        .timeline-title {
            font-weight: 500;
            margin-bottom: 3px;
            font-size: 13px;
        }
        .timeline-time {
            font-size: 11px;
            color: #9ca3af;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex">
            <i class="fas fa-signal mr-1"></i>
            <i class="fas fa-wifi mr-1"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="bg-white py-2 px-4 flex items-center">
        <div class="w-8">
            <i class="fas fa-arrow-left text-gray-600"></i>
        </div>
        <div class="text-center flex-1">
            <span class="font-medium text-lg">审核状态</span>
        </div>
        <div class="w-8"></div>
    </div>

    <!-- Progress Bar -->
    <div class="px-4 py-3 bg-white">
        <div class="flex justify-between text-xs text-gray-500 mb-2">
            <span>入驻进度</span>
            <span>7/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-4" style="height: calc(100vh - 150px);">
        <div class="verification-card p-3 mb-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-medium text-lg">川味小馆</h3>
                <div class="status-badge status-pending">审核中</div>
            </div>

            <div class="flex items-center text-sm text-gray-500 mb-4">
                <i class="fas fa-clock mr-2"></i>
                <span>预计审核时间：1-3个工作日</span>
            </div>

            <div class="bg-blue-50 p-3 rounded-lg text-sm text-blue-600 mb-4">
                <i class="fas fa-info-circle mr-2"></i>
                <span>您的申请已提交，我们将尽快进行审核，请耐心等待</span>
            </div>

            <div class="flex justify-between">
                <div class="text-center">
                    <div class="text-2xl font-medium text-gray-400">1</div>
                    <div class="text-xs text-gray-500">待审核</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-medium text-green-500">2</div>
                    <div class="text-xs text-gray-500">已提交</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-medium text-gray-400">0</div>
                    <div class="text-xs text-gray-500">已通过</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-medium text-gray-400">0</div>
                    <div class="text-xs text-gray-500">已驳回</div>
                </div>
            </div>
        </div>

        <h3 class="font-medium mb-4">审核进度</h3>

        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-dot completed">
                    <i class="fas fa-check"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">提交申请</div>
                    <div class="timeline-time">2023-06-15 14:30</div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-dot active">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">资质审核</div>
                    <div class="timeline-time">预计1-2个工作日</div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-dot">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">合同签署</div>
                    <div class="timeline-time">待审核通过后</div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-dot">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">结算设置</div>
                    <div class="timeline-time">待合同签署后</div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-dot">
                    <i class="fas fa-store"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">开业准备</div>
                    <div class="timeline-time">待结算设置完成后</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
        <button class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium">
            联系客服
        </button>
    </div>
</body>
</html>
