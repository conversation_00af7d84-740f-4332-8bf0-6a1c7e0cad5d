<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家详情</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 220px;
            background-color: #001529;
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 32px;
            height: 32px;
            background-color: #1890ff;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 16px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .menu-item.active {
            color: white;
            background-color: #1890ff;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-trigger {
            font-size: 18px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .header-trigger:hover {
            color: #1890ff;
        }
        
        .header-breadcrumb {
            margin-left: 24px;
            display: flex;
            align-items: center;
        }
        
        .breadcrumb-item {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item:not(:last-child)::after {
            content: "/";
            margin: 0 8px;
            color: #d9d9d9;
        }
        
        .breadcrumb-item:last-child {
            color: #333;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .header-icon:hover {
            color: #1890ff;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .page-actions {
            display: flex;
            align-items: center;
        }
        
        .action-button {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin-left: 8px;
        }
        
        .action-button i {
            margin-right: 8px;
        }
        
        .action-default {
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .action-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .action-primary {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        
        .action-primary:hover {
            background-color: #40a9ff;
        }
        
        .action-danger {
            background-color: white;
            color: #ff4d4f;
            border: 1px solid #ff4d4f;
        }
        
        .action-danger:hover {
            background-color: #ff4d4f;
            color: white;
        }
        
        .detail-layout {
            display: flex;
            gap: 24px;
        }
        
        .detail-main {
            flex: 3;
        }
        
        .detail-sidebar {
            flex: 1;
        }
        
        .detail-card {
            background-color: white;
            border-radius: 4px;
            margin-bottom: 24px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .card-actions {
            display: flex;
            align-items: center;
        }
        
        .card-action {
            color: #1890ff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 16px;
        }
        
        .card-action:hover {
            color: #40a9ff;
        }
        
        .card-content {
            padding: 24px;
        }
        
        .merchant-profile {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .merchant-logo {
            width: 80px;
            height: 80px;
            border-radius: 4px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 24px;
            color: #999;
            font-size: 32px;
        }
        
        .merchant-info {
            flex: 1;
        }
        
        .merchant-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .merchant-meta {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .merchant-id {
            font-size: 14px;
            color: #666;
            margin-right: 16px;
        }
        
        .merchant-category {
            font-size: 12px;
            padding: 2px 8px;
            background-color: #e6f7ff;
            color: #1890ff;
            border-radius: 2px;
        }
        
        .merchant-contact {
            font-size: 14px;
            color: #666;
        }
        
        .info-list {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .document-list {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }
        
        .document-item {
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .document-image {
            width: 100%;
            height: 120px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 32px;
        }
        
        .document-info {
            padding: 12px;
            border-top: 1px solid #f0f0f0;
        }
        
        .document-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .document-meta {
            font-size: 12px;
            color: #999;
        }
        
        .timeline {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        
        .timeline-item {
            position: relative;
            padding-bottom: 20px;
            padding-left: 20px;
        }
        
        .timeline-item::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #1890ff;
        }
        
        .timeline-item::after {
            content: "";
            position: absolute;
            left: 3px;
            top: 8px;
            width: 2px;
            height: calc(100% - 8px);
            background-color: #f0f0f0;
        }
        
        .timeline-item:last-child {
            padding-bottom: 0;
        }
        
        .timeline-item:last-child::after {
            display: none;
        }
        
        .timeline-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #999;
        }
        
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-store"></i>
                </div>
                <div class="sidebar-title">商家入驻系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user-check menu-icon"></i>
                    <span>审核管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span>统计报表</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统设置</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shield-alt menu-icon"></i>
                    <span>权限管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-file-alt menu-icon"></i>
                    <span>日志管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="header-trigger">
                        <i class="fas fa-bars"></i>
                    </div>
                    <div class="header-breadcrumb">
                        <span class="breadcrumb-item">商家管理</span>
                        <span class="breadcrumb-item">商家列表</span>
                        <span class="breadcrumb-item">商家详情</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">5</div>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <div class="page-title">品味小厨</div>
                    <div class="page-actions">
                        <button class="action-button action-default">
                            <i class="fas fa-edit"></i>
                            <span>编辑</span>
                        </button>
                        <button class="action-button action-primary">
                            <i class="fas fa-check"></i>
                            <span>审核通过</span>
                        </button>
                        <button class="action-button action-danger">
                            <i class="fas fa-times"></i>
                            <span>拒绝</span>
                        </button>
                    </div>
                </div>
                
                <div class="detail-layout">
                    <div class="detail-main">
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">基本信息</div>
                                <div class="card-actions">
                                    <span class="card-action">编辑</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="merchant-profile">
                                    <div class="merchant-logo">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <div class="merchant-info">
                                        <div class="merchant-name">品味小厨</div>
                                        <div class="merchant-meta">
                                            <div class="merchant-id">ID: 10001</div>
                                            <div class="merchant-category">餐饮</div>
                                        </div>
                                        <div class="merchant-contact">联系人: 张先生 (13812345678)</div>
                                    </div>
                                </div>
                                
                                <div class="info-list">
                                    <div class="info-item">
                                        <div class="info-label">商家类型</div>
                                        <div class="info-value">餐饮 - 中式快餐</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">营业状态</div>
                                        <div class="info-value"><span class="status-tag status-pending">待审核</span></div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">入驻时间</div>
                                        <div class="info-value">2023-05-18 14:23</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">营业地址</div>
                                        <div class="info-value">北京市朝阳区建国路88号</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">营业时间</div>
                                        <div class="info-value">周一至周日 10:00-22:00</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">联系电话</div>
                                        <div class="info-value">010-88889999</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">资质证明</div>
                                <div class="card-actions">
                                    <span class="card-action">查看原图</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="document-list">
                                    <div class="document-item">
                                        <div class="document-image">
                                            <i class="fas fa-file-image"></i>
                                        </div>
                                        <div class="document-info">
                                            <div class="document-name">营业执照</div>
                                            <div class="document-meta">上传时间: 2023-05-18 14:10</div>
                                        </div>
                                    </div>
                                    <div class="document-item">
                                        <div class="document-image">
                                            <i class="fas fa-file-image"></i>
                                        </div>
                                        <div class="document-info">
                                            <div class="document-name">食品经营许可证</div>
                                            <div class="document-meta">上传时间: 2023-05-18 14:12</div>
                                        </div>
                                    </div>
                                    <div class="document-item">
                                        <div class="document-image">
                                            <i class="fas fa-file-image"></i>
                                        </div>
                                        <div class="document-info">
                                            <div class="document-name">法人身份证正面</div>
                                            <div class="document-meta">上传时间: 2023-05-18 14:15</div>
                                        </div>
                                    </div>
                                    <div class="document-item">
                                        <div class="document-image">
                                            <i class="fas fa-file-image"></i>
                                        </div>
                                        <div class="document-info">
                                            <div class="document-name">法人身份证反面</div>
                                            <div class="document-meta">上传时间: 2023-05-18 14:15</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">商品信息</div>
                                <div class="card-actions">
                                    <span class="card-action">查看全部</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>商品名称</th>
                                            <th>分类</th>
                                            <th>价格</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>宫保鸡丁</td>
                                            <td>热菜</td>
                                            <td>¥38</td>
                                            <td><span class="status-tag status-approved">上架中</span></td>
                                            <td><a href="#" style="color: #1890ff;">查看</a></td>
                                        </tr>
                                        <tr>
                                            <td>水煮鱼</td>
                                            <td>热菜</td>
                                            <td>¥58</td>
                                            <td><span class="status-tag status-approved">上架中</span></td>
                                            <td><a href="#" style="color: #1890ff;">查看</a></td>
                                        </tr>
                                        <tr>
                                            <td>麻婆豆腐</td>
                                            <td>热菜</td>
                                            <td>¥28</td>
                                            <td><span class="status-tag status-approved">上架中</span></td>
                                            <td><a href="#" style="color: #1890ff;">查看</a></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-sidebar">
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">审核状态</div>
                            </div>
                            <div class="card-content">
                                <div class="text-center mb-4">
                                    <span class="status-tag status-pending text-base px-4 py-1">待审核</span>
                                </div>
                                
                                <ul class="timeline">
                                    <li class="timeline-item">
                                        <div class="timeline-title">提交入驻申请</div>
                                        <div class="timeline-time">2023-05-18 14:23</div>
                                    </li>
                                    <li class="timeline-item">
                                        <div class="timeline-title">上传资质证明</div>
                                        <div class="timeline-time">2023-05-18 14:15</div>
                                    </li>
                                    <li class="timeline-item">
                                        <div class="timeline-title">填写基本信息</div>
                                        <div class="timeline-time">2023-05-18 14:05</div>
                                    </li>
                                    <li class="timeline-item">
                                        <div class="timeline-title">注册账号</div>
                                        <div class="timeline-time">2023-05-18 14:00</div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">操作记录</div>
                            </div>
                            <div class="card-content">
                                <ul class="timeline">
                                    <li class="timeline-item">
                                        <div class="timeline-title">系统自动分配审核</div>
                                        <div class="timeline-time">2023-05-18 14:25</div>
                                    </li>
                                    <li class="timeline-item">
                                        <div class="timeline-title">提交入驻申请</div>
                                        <div class="timeline-time">2023-05-18 14:23</div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="detail-card">
                            <div class="card-header">
                                <div class="card-title">审核意见</div>
                            </div>
                            <div class="card-content">
                                <div class="form-group mb-4">
                                    <label class="form-label block mb-2 text-sm text-gray-700">审核结果</label>
                                    <select class="form-select w-full p-2 border border-gray-300 rounded">
                                        <option value="1">通过</option>
                                        <option value="2">拒绝</option>
                                        <option value="3">需补充资料</option>
                                    </select>
                                </div>
                                
                                <div class="form-group mb-4">
                                    <label class="form-label block mb-2 text-sm text-gray-700">审核意见</label>
                                    <textarea class="form-input w-full p-2 border border-gray-300 rounded" rows="4" placeholder="请输入审核意见"></textarea>
                                </div>
                                
                                <button class="action-button action-primary w-full justify-center">
                                    <span>提交审核</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
