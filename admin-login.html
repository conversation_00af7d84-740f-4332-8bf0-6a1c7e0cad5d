<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }
        
        .login-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: calc(100vh - 44px);
            padding: 0 24px;
        }
        
        .login-logo {
            margin-bottom: 40px;
            text-align: center;
        }
        
        .logo-image {
            width: 80px;
            height: 80px;
            background-color: #1890ff;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 40px;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .login-form {
            width: 100%;
            max-width: 320px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-input-prefix {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #bfbfbf;
        }
        
        .input-with-icon {
            padding-left: 40px;
        }
        
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
        }
        
        .checkbox {
            margin-right: 8px;
        }
        
        .forgot-password {
            color: #1890ff;
            font-size: 14px;
            text-decoration: none;
        }
        
        .login-button {
            width: 100%;
            padding: 12px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .login-button:hover {
            background-color: #40a9ff;
        }
        
        .login-button:active {
            background-color: #096dd9;
        }
        
        .other-login {
            margin-top: 24px;
            text-align: center;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 16px 0;
        }
        
        .divider-line {
            flex: 1;
            height: 1px;
            background-color: #e8e8e8;
        }
        
        .divider-text {
            padding: 0 16px;
            color: #bfbfbf;
            font-size: 14px;
        }
        
        .social-login {
            display: flex;
            justify-content: center;
            margin-top: 16px;
        }
        
        .social-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 8px;
            color: #333;
            font-size: 18px;
            transition: all 0.3s;
        }
        
        .social-icon:hover {
            background-color: #e8e8e8;
        }
        
        .register-link {
            margin-top: 24px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        
        .register-link a {
            color: #1890ff;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <div class="login-container">
        <div class="login-logo">
            <div class="logo-image">
                <i class="fas fa-store"></i>
            </div>
            <div class="logo-text">商家入驻系统管理后台</div>
        </div>
        
        <div class="login-form">
            <div class="form-group">
                <label class="form-label">用户名</label>
                <div class="form-input-prefix">
                    <i class="fas fa-user input-icon"></i>
                    <input type="text" class="form-input input-with-icon" placeholder="请输入管理员用户名">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">密码</label>
                <div class="form-input-prefix">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" class="form-input input-with-icon" placeholder="请输入密码">
                </div>
            </div>
            
            <div class="remember-forgot">
                <div class="remember-me">
                    <input type="checkbox" id="remember" class="checkbox">
                    <label for="remember" class="text-sm text-gray-600">记住我</label>
                </div>
                <a href="#" class="forgot-password">忘记密码？</a>
            </div>
            
            <button class="login-button">登录</button>
            
            <div class="other-login">
                <div class="divider">
                    <div class="divider-line"></div>
                    <span class="divider-text">其他登录方式</span>
                    <div class="divider-line"></div>
                </div>
                
                <div class="social-login">
                    <div class="social-icon">
                        <i class="fab fa-weixin" style="color: #07C160;"></i>
                    </div>
                    <div class="social-icon">
                        <i class="fas fa-mobile-alt" style="color: #1890ff;"></i>
                    </div>
                    <div class="social-icon">
                        <i class="fas fa-qrcode" style="color: #333;"></i>
                    </div>
                </div>
            </div>
            
            <div class="register-link">
                还没有账号？<a href="#">联系系统管理员</a>
            </div>
        </div>
    </div>
</body>
</html>
