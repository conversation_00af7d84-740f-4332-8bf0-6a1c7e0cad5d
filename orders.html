<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            width: 1440px;
            margin: 0;
            padding: 0;
        }
        .order-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border-left: 4px solid #3b82f6;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-pending { background: #fef3c7; color: #d97706; }
        .status-confirmed { background: #dbeafe; color: #2563eb; }
        .status-preparing { background: #e0e7ff; color: #5b21b6; }
        .status-delivering { background: #fef3c7; color: #ea580c; }
        .status-completed { background: #d1fae5; color: #059669; }
        .status-cancelled { background: #fee2e2; color: #dc2626; }
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { transform: translateY(-1px); }
        .form-input, .form-select {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.2s;
            background: #fafbfc;
        }
        .form-input:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
            background: white;
        }
        .metric-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }
        .timeline {
            position: relative;
            padding-left: 20px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 16px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -24px;
            top: 4px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #3b82f6;
        }
        .timeline-item.completed::before {
            background: #10b981;
        }
        .timeline-item.current::before {
            background: #f59e0b;
            box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.2);
        }
        .order-items {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
        }
        .item-row {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .item-row:last-child {
            border-bottom: none;
        }
        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }
        .priority-high { background: #fee2e2; color: #dc2626; }
        .priority-normal { background: #f3f4f6; color: #6b7280; }
        .priority-low { background: #ecfdf5; color: #059669; }
        .tab-button {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        .tab-button.active {
            background: #3b82f6;
            color: white;
        }
        .tab-button:not(.active) {
            background: #f1f5f9;
            color: #64748b;
        }
        .tab-button:not(.active):hover {
            background: #e2e8f0;
            color: #475569;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-8">
        <!-- 页面标题和操作 -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">订单管理</h1>
                <p class="text-gray-500 mt-2">管理校园外卖平台订单状态和配送流程</p>
            </div>
            <div class="flex gap-3">
                <button class="btn btn-warning">
                    <i class="fas fa-exclamation-triangle mr-2"></i>异常订单 (8)
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-download mr-2"></i>导出数据
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-6 gap-6 mb-8">
            <div class="metric-card">
                <div class="text-3xl font-bold text-gray-900 mb-2">1,247</div>
                <div class="text-sm text-gray-500">今日订单</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-orange-600 mb-2">234</div>
                <div class="text-sm text-gray-500">进行中</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-blue-600 mb-2">156</div>
                <div class="text-sm text-gray-500">配送中</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-green-600 mb-2">1,205</div>
                <div class="text-sm text-gray-500">已完成</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-red-600 mb-2">8</div>
                <div class="text-sm text-gray-500">异常订单</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-purple-600 mb-2">96.8%</div>
                <div class="text-sm text-gray-500">成功率</div>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="flex gap-2 mb-6">
            <button class="tab-button active">全部订单</button>
            <button class="tab-button">待确认</button>
            <button class="tab-button">制作中</button>
            <button class="tab-button">配送中</button>
            <button class="tab-button">已完成</button>
            <button class="tab-button">异常订单</button>
        </div>

        <!-- 筛选区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="grid grid-cols-6 gap-4">
                <select class="form-select">
                    <option>全部状态</option>
                    <option>待确认</option>
                    <option>制作中</option>
                    <option>配送中</option>
                    <option>已完成</option>
                </select>
                <select class="form-select">
                    <option>全部商家</option>
                    <option>麻辣香锅店</option>
                    <option>新鲜水果店</option>
                    <option>便民超市</option>
                </select>
                <select class="form-select">
                    <option>全部配送员</option>
                    <option>王师傅</option>
                    <option>李师傅</option>
                    <option>张师傅</option>
                </select>
                <input type="date" class="form-input">
                <input type="text" placeholder="搜索订单号..." class="form-input">
                <button class="btn btn-primary">
                    <i class="fas fa-search mr-2"></i>搜索
                </button>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="space-y-6">
            <!-- 订单1 -->
            <div class="order-card">
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-center gap-4">
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">#ORD20240220001</h3>
                            <p class="text-sm text-gray-500">2024-02-20 14:30:25</p>
                        </div>
                        <span class="status-badge status-delivering">配送中</span>
                        <span class="priority-badge priority-high">高优先级</span>
                    </div>
                    <div class="flex gap-2">
                        <button class="btn btn-primary">详情</button>
                        <button class="btn btn-warning">联系</button>
                        <button class="btn btn-success">完成</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-4 gap-6">
                    <!-- 商家信息 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">商家信息</h4>
                        <div class="flex items-center mb-2">
                            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-utensils text-orange-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">麻辣香锅店</div>
                                <div class="text-sm text-gray-500">张老板 | 138****5678</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-500">
                            <div>地址: 东区美食街12号</div>
                            <div>距离: 1.2公里</div>
                        </div>
                    </div>
                    
                    <!-- 用户信息 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">用户信息</h4>
                        <div class="flex items-center mb-2">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">李同学</div>
                                <div class="text-sm text-gray-500">138****5678</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-500">
                            <div>地址: 东区宿舍楼A座301</div>
                            <div>备注: 到楼下电话联系</div>
                        </div>
                    </div>
                    
                    <!-- 配送信息 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">配送信息</h4>
                        <div class="flex items-center mb-2">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-motorcycle text-green-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">王师傅</div>
                                <div class="text-sm text-gray-500">139****1234</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-500">
                            <div>预计送达: 15:00</div>
                            <div>配送距离: 0.8公里</div>
                        </div>
                    </div>
                    
                    <!-- 订单金额 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">订单金额</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">商品金额</span>
                                <span class="font-medium">¥55.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">配送费</span>
                                <span class="font-medium">¥3.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">优惠券</span>
                                <span class="font-medium text-red-500">-¥5.00</span>
                            </div>
                            <div class="border-t pt-2">
                                <div class="flex justify-between">
                                    <span class="font-semibold">实付金额</span>
                                    <span class="font-bold text-lg text-red-600">¥53.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 订单商品 -->
                <div class="order-items">
                    <h4 class="font-semibold text-gray-900 mb-3">订单商品</h4>
                    <div class="item-row">
                        <div class="flex-1">麻辣香锅（中辣）</div>
                        <div class="text-gray-500">×2</div>
                        <div class="font-medium">¥36.00</div>
                    </div>
                    <div class="item-row">
                        <div class="flex-1">米饭</div>
                        <div class="text-gray-500">×1</div>
                        <div class="font-medium">¥3.00</div>
                    </div>
                    <div class="item-row">
                        <div class="flex-1">可乐</div>
                        <div class="text-gray-500">×2</div>
                        <div class="font-medium">¥16.00</div>
                    </div>
                </div>
                
                <!-- 订单时间线 -->
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">订单进度</h4>
                    <div class="timeline">
                        <div class="timeline-item completed">
                            <div class="text-sm font-medium text-gray-900">订单确认</div>
                            <div class="text-xs text-gray-500">2024-02-20 14:30</div>
                        </div>
                        <div class="timeline-item completed">
                            <div class="text-sm font-medium text-gray-900">商家接单</div>
                            <div class="text-xs text-gray-500">2024-02-20 14:32</div>
                        </div>
                        <div class="timeline-item completed">
                            <div class="text-sm font-medium text-gray-900">开始制作</div>
                            <div class="text-xs text-gray-500">2024-02-20 14:35</div>
                        </div>
                        <div class="timeline-item current">
                            <div class="text-sm font-medium text-gray-900">配送中</div>
                            <div class="text-xs text-gray-500">2024-02-20 14:45</div>
                        </div>
                        <div class="timeline-item">
                            <div class="text-sm font-medium text-gray-500">送达完成</div>
                            <div class="text-xs text-gray-500">预计 15:00</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单2 -->
            <div class="order-card">
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-center gap-4">
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">#ORD20240220002</h3>
                            <p class="text-sm text-gray-500">2024-02-20 14:25:18</p>
                        </div>
                        <span class="status-badge status-confirmed">已确认</span>
                        <span class="priority-badge priority-normal">普通</span>
                    </div>
                    <div class="flex gap-2">
                        <button class="btn btn-success">分配配送员</button>
                        <button class="btn btn-primary">详情</button>
                        <button class="btn btn-danger">取消订单</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-4 gap-6">
                    <!-- 商家信息 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">商家信息</h4>
                        <div class="flex items-center mb-2">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-apple-alt text-green-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">新鲜水果店</div>
                                <div class="text-sm text-gray-500">李经理 | 139****1234</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-500">
                            <div>地址: 西区商业街8号</div>
                            <div>距离: 0.9公里</div>
                        </div>
                    </div>
                    
                    <!-- 用户信息 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">用户信息</h4>
                        <div class="flex items-center mb-2">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">张同学</div>
                                <div class="text-sm text-gray-500">139****1234</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-500">
                            <div>地址: 西区宿舍楼B座205</div>
                            <div>备注: 无</div>
                        </div>
                    </div>
                    
                    <!-- 配送信息 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">配送信息</h4>
                        <div class="flex items-center mb-2">
                            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-clock text-gray-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-orange-600">待分配配送员</div>
                                <div class="text-sm text-gray-500">系统自动分配中</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-500">
                            <div>预计送达: 15:10</div>
                            <div>配送距离: 1.1公里</div>
                        </div>
                    </div>
                    
                    <!-- 订单金额 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">订单金额</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">商品金额</span>
                                <span class="font-medium">¥39.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">配送费</span>
                                <span class="font-medium">¥3.00</span>
                            </div>
                            <div class="border-t pt-2">
                                <div class="flex justify-between">
                                    <span class="font-semibold">实付金额</span>
                                    <span class="font-bold text-lg text-red-600">¥42.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 订单商品 -->
                <div class="order-items">
                    <h4 class="font-semibold text-gray-900 mb-3">订单商品</h4>
                    <div class="item-row">
                        <div class="flex-1">苹果（红富士）</div>
                        <div class="text-gray-500">×3斤</div>
                        <div class="font-medium">¥24.00</div>
                    </div>
                    <div class="item-row">
                        <div class="flex-1">香蕉</div>
                        <div class="text-gray-500">×2斤</div>
                        <div class="font-medium">¥15.00</div>
                    </div>
                </div>
                
                <!-- 订单时间线 -->
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">订单进度</h4>
                    <div class="timeline">
                        <div class="timeline-item completed">
                            <div class="text-sm font-medium text-gray-900">订单确认</div>
                            <div class="text-xs text-gray-500">2024-02-20 14:25</div>
                        </div>
                        <div class="timeline-item current">
                            <div class="text-sm font-medium text-gray-900">商家接单</div>
                            <div class="text-xs text-gray-500">2024-02-20 14:27</div>
                        </div>
                        <div class="timeline-item">
                            <div class="text-sm font-medium text-gray-500">分配配送员</div>
                            <div class="text-xs text-gray-500">等待中</div>
                        </div>
                        <div class="timeline-item">
                            <div class="text-sm font-medium text-gray-500">开始配送</div>
                            <div class="text-xs text-gray-500">-</div>
                        </div>
                        <div class="timeline-item">
                            <div class="text-sm font-medium text-gray-500">送达完成</div>
                            <div class="text-xs text-gray-500">预计 15:10</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="mt-8 flex justify-between items-center">
            <div class="text-sm text-gray-500">
                显示 1-2 条，共 1,247 条记录
            </div>
            <div class="flex gap-2">
                <button class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">上一页</button>
                <button class="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm">1</button>
                <button class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">2</button>
                <button class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">3</button>
                <button class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">下一页</button>
            </div>
        </div>
    </div>
</body>
</html>
