<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 220px;
            background-color: #001529;
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 32px;
            height: 32px;
            background-color: #1890ff;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 16px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .menu-item.active {
            color: white;
            background-color: #1890ff;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-trigger {
            font-size: 18px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .header-trigger:hover {
            color: #1890ff;
        }
        
        .header-search {
            margin-left: 24px;
            position: relative;
        }
        
        .search-input {
            padding: 8px 12px 8px 36px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 240px;
            transition: all 0.3s;
        }
        
        .search-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #bfbfbf;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .header-icon:hover {
            color: #1890ff;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .page-description {
            font-size: 14px;
            color: #666;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .trend-up {
            color: #52c41a;
        }
        
        .trend-down {
            color: #ff4d4f;
        }
        
        .trend-icon {
            margin-right: 4px;
        }
        
        .chart-cards {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .chart-card {
            background-color: white;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .chart-actions {
            display: flex;
            align-items: center;
        }
        
        .chart-action {
            color: #666;
            font-size: 14px;
            margin-left: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .chart-action:hover {
            color: #1890ff;
        }
        
        .chart-content {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #bfbfbf;
        }
        
        .table-card {
            background-color: white;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .table-actions {
            display: flex;
            align-items: center;
        }
        
        .table-action {
            color: #666;
            font-size: 14px;
            margin-left: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .table-action:hover {
            color: #1890ff;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-store"></i>
                </div>
                <div class="sidebar-title">商家入驻系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item active">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user-check menu-icon"></i>
                    <span>审核管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span>统计报表</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统设置</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shield-alt menu-icon"></i>
                    <span>权限管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-file-alt menu-icon"></i>
                    <span>日志管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="header-trigger">
                        <i class="fas fa-bars"></i>
                    </div>
                    <div class="header-search">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索...">
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">5</div>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">仪表盘</h1>
                    <p class="page-description">欢迎回来，这里是您的数据概览</p>
                </div>
                
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-title">总商家数</div>
                        <div class="stat-value">1,286</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>16.8% 较上月</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">待审核商家</div>
                        <div class="stat-value">42</div>
                        <div class="stat-trend trend-down">
                            <i class="fas fa-arrow-down trend-icon"></i>
                            <span>8.2% 较上月</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">今日订单数</div>
                        <div class="stat-value">3,754</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>12.5% 较昨日</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">今日交易额</div>
                        <div class="stat-value">¥89,432</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>9.3% 较昨日</span>
                        </div>
                    </div>
                </div>
                
                <div class="chart-cards">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">商家入驻趋势</div>
                            <div class="chart-actions">
                                <span class="chart-action">本周</span>
                                <span class="chart-action">本月</span>
                                <span class="chart-action">全年</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-line" style="font-size: 80px;"></i>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">商家类型分布</div>
                            <div class="chart-actions">
                                <i class="fas fa-ellipsis-v chart-action"></i>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-pie" style="font-size: 80px;"></i>
                        </div>
                    </div>
                </div>
                
                <div class="table-card">
                    <div class="table-header">
                        <div class="table-title">最近入驻商家</div>
                        <div class="table-actions">
                            <span class="table-action">查看全部</span>
                        </div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>商家名称</th>
                                <th>行业类型</th>
                                <th>联系人</th>
                                <th>申请时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>品味小厨</td>
                                <td>餐饮</td>
                                <td>张先生 (13812345678)</td>
                                <td>2023-05-18 14:23</td>
                                <td><span class="status-tag status-pending">待审核</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                            <tr>
                                <td>鲜花坊</td>
                                <td>零售</td>
                                <td>李小姐 (13987654321)</td>
                                <td>2023-05-18 11:05</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                            <tr>
                                <td>美丽发廊</td>
                                <td>美容美发</td>
                                <td>王女士 (13765432198)</td>
                                <td>2023-05-17 16:42</td>
                                <td><span class="status-tag status-rejected">已拒绝</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                            <tr>
                                <td>健身工作室</td>
                                <td>健身</td>
                                <td>刘先生 (13612345678)</td>
                                <td>2023-05-17 09:18</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                            <tr>
                                <td>快乐童装</td>
                                <td>零售</td>
                                <td>赵女士 (13587654321)</td>
                                <td>2023-05-16 15:37</td>
                                <td><span class="status-tag status-approved">已通过</span></td>
                                <td><a href="#" style="color: #1890ff;">查看</a></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
