<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营业时间设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }

        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }

        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }

        .wechat-nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
        }

        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Progress Bar */
        .progress-container {
            padding: 12px 16px;
            background-color: #ffffff;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }

        .progress-bar {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background-color: #07C160;
            width: 100%;
        }

        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px - 57px);
            padding: 16px;
            background-color: #f6f6f6;
            overflow-y: auto;
        }

        .content-description {
            font-size: 13px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.4;
        }

        /* Business Hours Card */
        .hours-card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .hours-title {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            margin-bottom: 12px;
        }

        .day-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .day-name {
            width: 60px;
            font-size: 14px;
            color: #333;
        }

        .time-selector {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .time-input {
            width: 80px;
            padding: 8px;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            font-size: 13px;
            text-align: center;
        }

        .time-separator {
            margin: 0 8px;
            color: #999;
        }

        .day-toggle {
            margin-left: 12px;
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .toggle-input:checked + .toggle-slider {
            background-color: #07C160;
        }

        .toggle-input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        /* Special Hours */
        .special-hours {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px dashed #e5e5e5;
        }

        .special-day {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 8px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .special-date {
            font-size: 13px;
            color: #333;
            margin-right: 8px;
            width: 80px;
        }

        .special-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-right: auto;
        }

        .special-status.open {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .special-status.closed {
            background-color: #fff1f0;
            color: #ff4d4f;
        }

        .special-actions {
            display: flex;
            align-items: center;
        }

        .special-action {
            font-size: 12px;
            color: #1890ff;
            margin-left: 8px;
            cursor: pointer;
        }

        .add-special {
            display: flex;
            align-items: center;
            color: #07C160;
            font-size: 13px;
            margin-top: 8px;
            cursor: pointer;
        }

        .add-special i {
            margin-right: 4px;
        }

        /* Bottom Button */
        .bottom-button {
            padding: 12px 16px;
            background-color: #ffffff;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 50px;
            width: 100%;
            box-sizing: border-box;
        }

        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
        }

        /* Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }

        .tab-item.active {
            color: #07C160;
        }

        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="wechat-nav-title">营业时间设置</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-info">
            <span>入驻进度</span>
            <span>10/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <p class="content-description">请设置您的常规营业时间和特殊日期安排，顾客可根据您设置的时间下单</p>

        <div class="hours-card">
            <h3 class="hours-title">常规营业时间</h3>
            
            <div class="day-row">
                <div class="day-name">周一</div>
                <div class="time-selector">
                    <input type="text" class="time-input" value="09:00">
                    <span class="time-separator">至</span>
                    <input type="text" class="time-input" value="21:00">
                </div>
                <label class="day-toggle">
                    <input type="checkbox" class="toggle-input" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>

            <div class="day-row">
                <div class="day-name">周二</div>
                <div class="time-selector">
                    <input type="text" class="time-input" value="09:00">
                    <span class="time-separator">至</span>
                    <input type="text" class="time-input" value="21:00">
                </div>
                <label class="day-toggle">
                    <input type="checkbox" class="toggle-input" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>

            <div class="day-row">
                <div class="day-name">周三</div>
                <div class="time-selector">
                    <input type="text" class="time-input" value="09:00">
                    <span class="time-separator">至</span>
                    <input type="text" class="time-input" value="21:00">
                </div>
                <label class="day-toggle">
                    <input type="checkbox" class="toggle-input" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>

            <div class="day-row">
                <div class="day-name">周四</div>
                <div class="time-selector">
                    <input type="text" class="time-input" value="09:00">
                    <span class="time-separator">至</span>
                    <input type="text" class="time-input" value="21:00">
                </div>
                <label class="day-toggle">
                    <input type="checkbox" class="toggle-input" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>

            <div class="day-row">
                <div class="day-name">周五</div>
                <div class="time-selector">
                    <input type="text" class="time-input" value="09:00">
                    <span class="time-separator">至</span>
                    <input type="text" class="time-input" value="22:00">
                </div>
                <label class="day-toggle">
                    <input type="checkbox" class="toggle-input" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>

            <div class="day-row">
                <div class="day-name">周六</div>
                <div class="time-selector">
                    <input type="text" class="time-input" value="10:00">
                    <span class="time-separator">至</span>
                    <input type="text" class="time-input" value="22:00">
                </div>
                <label class="day-toggle">
                    <input type="checkbox" class="toggle-input" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>

            <div class="day-row">
                <div class="day-name">周日</div>
                <div class="time-selector">
                    <input type="text" class="time-input" value="10:00">
                    <span class="time-separator">至</span>
                    <input type="text" class="time-input" value="21:00">
                </div>
                <label class="day-toggle">
                    <input type="checkbox" class="toggle-input" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>
        </div>

        <div class="hours-card">
            <h3 class="hours-title">特殊日期安排</h3>
            <p class="content-description">设置节假日或临时调整的营业时间</p>

            <div class="special-hours">
                <div class="special-day">
                    <div class="special-date">2023-05-01</div>
                    <div class="special-status closed">不营业</div>
                    <div class="special-actions">
                        <span class="special-action">编辑</span>
                        <span class="special-action">删除</span>
                    </div>
                </div>

                <div class="special-day">
                    <div class="special-date">2023-05-20</div>
                    <div class="special-status open">10:00-23:00</div>
                    <div class="special-actions">
                        <span class="special-action">编辑</span>
                        <span class="special-action">删除</span>
                    </div>
                </div>

                <div class="add-special">
                    <i class="fas fa-plus-circle"></i>
                    <span>添加特殊日期</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="bottom-button">
        <button class="btn-primary">保存设置</button>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
