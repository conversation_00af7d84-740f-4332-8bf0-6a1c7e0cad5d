<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收银设备安装</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }

        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }

        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }

        .wechat-nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
        }

        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Progress Bar */
        .progress-container {
            padding: 12px 16px;
            background-color: #ffffff;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }

        .progress-bar {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background-color: #07C160;
            width: 100%;
        }

        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px - 57px);
            padding: 16px;
            background-color: #f6f6f6;
            overflow-y: auto;
        }

        .content-description {
            font-size: 13px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.4;
        }

        /* Device Card */
        .device-card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .device-title {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            margin-bottom: 12px;
        }

        .device-option {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .device-option:hover {
            border-color: #07C160;
            background-color: #f0fdf4;
        }

        .device-option.selected {
            border-color: #07C160;
            background-color: #f0fdf4;
        }

        .device-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 20px;
            color: #07C160;
        }

        .device-info {
            flex: 1;
        }

        .device-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .device-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .device-price {
            font-size: 14px;
            font-weight: 500;
            color: #ff4d4f;
            margin-left: 12px;
        }

        .device-price.free {
            color: #07C160;
        }

        .device-radio {
            width: 18px;
            height: 18px;
            border: 2px solid #d9d9d9;
            border-radius: 50%;
            margin-left: 12px;
            position: relative;
        }

        .device-option.selected .device-radio {
            border-color: #07C160;
        }

        .device-option.selected .device-radio::after {
            content: "";
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #07C160;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Step Card */
        .step-card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .step-title {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            margin-bottom: 12px;
        }

        .step-list {
            counter-reset: step-counter;
        }

        .step-item {
            display: flex;
            margin-bottom: 16px;
            position: relative;
            padding-left: 32px;
        }

        .step-item::before {
            counter-increment: step-counter;
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background-color: #07C160;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
        }

        .step-content {
            flex: 1;
        }

        .step-heading {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .step-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .step-image {
            width: 100%;
            height: 120px;
            background-color: #f5f5f5;
            border-radius: 4px;
            margin-top: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }

        /* Bottom Button */
        .bottom-button {
            padding: 12px 16px;
            background-color: #ffffff;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 50px;
            width: 100%;
            box-sizing: border-box;
        }

        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
        }

        /* Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }

        .tab-item.active {
            color: #07C160;
        }

        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="wechat-nav-title">收银设备安装</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-info">
            <span>入驻进度</span>
            <span>10/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <p class="content-description">请选择适合您店铺的收银设备，并按照指引完成安装和测试</p>

        <div class="device-card">
            <h3 class="device-title">选择收银设备</h3>
            
            <div class="device-option selected">
                <div class="device-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="device-info">
                    <div class="device-name">手机收银</div>
                    <div class="device-desc">使用您的手机作为收银设备，无需额外硬件</div>
                </div>
                <div class="device-price free">免费</div>
                <div class="device-radio"></div>
            </div>

            <div class="device-option">
                <div class="device-icon">
                    <i class="fas fa-print"></i>
                </div>
                <div class="device-info">
                    <div class="device-name">蓝牙打印机套装</div>
                    <div class="device-desc">包含蓝牙小票打印机，支持自动打印订单</div>
                </div>
                <div class="device-price">¥299</div>
                <div class="device-radio"></div>
            </div>

            <div class="device-option">
                <div class="device-icon">
                    <i class="fas fa-cash-register"></i>
                </div>
                <div class="device-info">
                    <div class="device-name">智能收银机</div>
                    <div class="device-desc">一体式收银机，包含打印机和扫码器</div>
                </div>
                <div class="device-price">¥1299</div>
                <div class="device-radio"></div>
            </div>
        </div>

        <div class="step-card">
            <h3 class="step-title">安装步骤</h3>
            
            <div class="step-list">
                <div class="step-item">
                    <div class="step-content">
                        <div class="step-heading">下载收银App</div>
                        <div class="step-desc">在应用商店搜索"微信商家助手"并下载安装</div>
                        <div class="step-image">
                            <i class="fas fa-download"></i> 应用下载示意图
                        </div>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-content">
                        <div class="step-heading">登录商家账号</div>
                        <div class="step-desc">使用您的商家账号登录App，确认店铺信息</div>
                        <div class="step-image">
                            <i class="fas fa-user-check"></i> 登录界面示意图
                        </div>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-content">
                        <div class="step-heading">连接打印设备（可选）</div>
                        <div class="step-desc">如果您选择了打印设备，请按照App指引完成蓝牙连接</div>
                        <div class="step-image">
                            <i class="fas fa-bluetooth"></i> 蓝牙连接示意图
                        </div>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-content">
                        <div class="step-heading">测试收银功能</div>
                        <div class="step-desc">创建测试订单，确认收银和打印功能正常</div>
                        <div class="step-image">
                            <i class="fas fa-check-circle"></i> 测试成功示意图
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="bottom-button">
        <button class="btn-primary">完成设置</button>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
