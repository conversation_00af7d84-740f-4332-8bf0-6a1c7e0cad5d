<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资质上传 - 商家入驻</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f6f6f6;
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        /* iOS Status Bar */
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }

        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }

        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }

        .wechat-nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
        }

        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Progress Bar */
        .progress-container {
            padding: 12px 16px;
            background-color: #ffffff;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }

        .progress-bar {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background-color: #07C160;
            width: 30%;
        }

        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px - 57px);
            padding: 16px;
            background-color: #f6f6f6;
            display: flex;
            flex-direction: column;
        }

        .content-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        /* Qualification Card */
        .qualification-card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .qualification-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .qualification-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        .required-tag {
            background-color: #FFF1F0;
            color: #FF4D4F;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
        }

        .qualification-desc {
            font-size: 11px;
            color: #999;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        /* Upload Box */
        .upload-box {
            border: 1px dashed #DEDEDE;
            border-radius: 4px;
            background-color: #FAFAFA;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            height: 90px;
        }

        .upload-box:active {
            background-color: #F5F5F5;
            border-color: #CCCCCC;
        }

        .upload-icon {
            font-size: 20px;
            color: #BFBFBF;
            margin-bottom: 6px;
        }

        .upload-text {
            font-size: 13px;
            color: #999;
        }

        .upload-hint {
            font-size: 11px;
            color: #BFBFBF;
            margin-top: 3px;
            text-align: center;
        }

        /* Uploaded Image */
        .uploaded-container {
            position: relative;
            margin-bottom: 8px;
        }

        .uploaded-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
        }

        .image-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }

        .image-name {
            font-size: 12px;
            color: #999;
        }

        .image-buttons {
            display: flex;
        }

        .image-button {
            font-size: 12px;
            padding: 0 8px;
            border: none;
            background: none;
        }

        .image-button.edit {
            color: #1890FF;
        }

        .image-button.delete {
            color: #FF4D4F;
        }

        /* ID Card Upload */
        .id-card-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 8px;
        }

        .id-card-box {
            border: 1px dashed #DEDEDE;
            border-radius: 4px;
            background-color: #FAFAFA;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 12px;
            cursor: pointer;
            height: 80px;
        }

        .id-card-icon {
            font-size: 18px;
            color: #BFBFBF;
            margin-bottom: 6px;
        }

        .id-card-text {
            font-size: 11px;
            color: #999;
        }

        /* Bottom Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }

        .tab-item.active {
            color: #07C160;
        }

        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }

        /* Bottom Button */
        .bottom-button {
            padding: 12px 16px;
            background-color: #ffffff;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 50px;
            width: 100%;
            box-sizing: border-box;
        }

        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="wechat-nav-title">资质上传</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-info">
            <span>入驻进度</span>
            <span>3/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <p class="content-description">请上传您的营业资质证明，确保图片清晰可见，信息完整</p>

        <div class="qualification-card">
            <div class="qualification-header">
                <span class="qualification-title">营业执照</span>
                <span class="required-tag">必传</span>
            </div>
            <p class="qualification-desc">请上传营业执照正本或副本，确保所有信息清晰可见</p>

            <div class="upload-box">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <div class="upload-text">点击上传图片</div>
                <div class="upload-hint">支持JPG、PNG格式，大小不超过5MB</div>
            </div>
        </div>

        <div class="qualification-card">
            <div class="qualification-header">
                <span class="qualification-title">食品经营许可证</span>
                <span class="required-tag">必传</span>
            </div>
            <p class="qualification-desc">适用于餐饮类商家，需上传有效期内的许可证</p>

            <div class="uploaded-container">
                <img src="https://images.unsplash.com/photo-1568219656418-15c329312bf1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" class="uploaded-image">
                <div class="image-actions">
                    <span class="image-name">food_license.jpg</span>
                    <div class="image-buttons">
                        <button class="image-button edit">重新上传</button>
                        <button class="image-button delete">删除</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="qualification-card">
            <div class="qualification-header">
                <span class="qualification-title">法人身份证</span>
                <span class="required-tag">必传</span>
            </div>
            <p class="qualification-desc">请上传法人身份证正反面，确保证件在有效期内</p>

            <div class="id-card-container">
                <div class="id-card-box">
                    <i class="fas fa-id-card id-card-icon"></i>
                    <span class="id-card-text">身份证正面</span>
                </div>
                <div class="id-card-box">
                    <i class="fas fa-id-card-alt id-card-icon"></i>
                    <span class="id-card-text">身份证反面</span>
                </div>
            </div>
        </div>

        <div class="qualification-card">
            <div class="qualification-header">
                <span class="qualification-title">卫生许可证</span>
            </div>
            <p class="qualification-desc">适用于餐饮、美容美发等行业，需上传有效期内的许可证</p>

            <div class="upload-box">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <div class="upload-text">点击上传图片</div>
                <div class="upload-hint">支持JPG、PNG格式，大小不超过5MB</div>
            </div>
        </div>

        <div class="qualification-card">
            <div class="qualification-header">
                <span class="qualification-title">其他资质证明</span>
            </div>
            <p class="qualification-desc">可上传其他相关资质证明，如特殊行业许可证等</p>

            <div class="upload-box">
                <i class="fas fa-plus upload-icon"></i>
                <div class="upload-text">添加更多资质</div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="bottom-button">
        <button class="btn-primary">保存并继续</button>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
