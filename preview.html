<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息预览</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
        }
        .status-bar::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 24px;
            background-color: #000;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .progress-bar {
            height: 4px;
            background-color: #e5e5e5;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #07c160;
            width: 60%;
        }
        .preview-section {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            margin-bottom: 16px;
        }
        .section-header {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-title {
            font-weight: 500;
            font-size: 15px;
        }
        .section-edit {
            color: #07c160;
            font-size: 14px;
        }
        .section-content {
            padding: 16px;
        }
        .info-item {
            display: flex;
            margin-bottom: 12px;
        }
        .info-item:last-child {
            margin-bottom: 0;
        }
        .info-label {
            width: 80px;
            color: #666;
            font-size: 14px;
        }
        .info-value {
            flex: 1;
            font-size: 14px;
        }
        .store-banner {
            height: 120px;
            background-image: url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
            background-size: cover;
            background-position: center;
            position: relative;
        }
        .store-logo {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            background-color: white;
            background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60');
            background-size: cover;
            background-position: center;
            position: absolute;
            bottom: -25px;
            left: 16px;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .tag {
            display: inline-block;
            padding: 3px 6px;
            background-color: #f3f4f6;
            border-radius: 4px;
            font-size: 10px;
            margin-right: 6px;
            margin-bottom: 6px;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }
        .product-item {
            background-color: #f9fafb;
            border-radius: 8px;
            overflow: hidden;
        }
        .product-img {
            height: 80px;
            background-size: cover;
            background-position: center;
        }
        .product-info {
            padding: 8px;
        }
        .product-name {
            font-size: 13px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        .product-price {
            color: #ef4444;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex">
            <i class="fas fa-signal mr-1"></i>
            <i class="fas fa-wifi mr-1"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="bg-white py-2 px-4 flex items-center">
        <div class="w-8">
            <i class="fas fa-arrow-left text-gray-600"></i>
        </div>
        <div class="text-center flex-1">
            <span class="font-medium text-lg">信息预览</span>
        </div>
        <div class="w-8"></div>
    </div>

    <!-- Progress Bar -->
    <div class="px-4 py-3 bg-white">
        <div class="flex justify-between text-xs text-gray-500 mb-2">
            <span>入驻进度</span>
            <span>6/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-4" style="height: calc(100vh - 150px);">
        <p class="text-gray-600 mb-3 text-xs">请确认您的店铺信息，这将是顾客看到的店铺页面</p>

        <!-- Store Preview -->
        <div class="preview-section">
            <div class="store-banner">
                <div class="store-logo"></div>
            </div>

            <div class="pt-8 px-4 pb-3">
                <h2 class="text-lg font-bold mb-1">川味小馆</h2>
                <div class="flex items-center text-xs text-gray-500 mb-2">
                    <i class="fas fa-star text-yellow-400 mr-1"></i>
                    <span class="mr-2">暂无评分</span>
                    <span>人均: ¥75</span>
                </div>

                <div class="flex flex-wrap mb-2">
                    <div class="tag">中餐</div>
                    <div class="tag">川菜</div>
                    <div class="tag">家常菜</div>
                </div>

                <div class="flex items-center text-xs text-gray-500">
                    <i class="fas fa-map-marker-alt mr-1"></i>
                    <span>北京市朝阳区建国路88号</span>
                </div>
            </div>
        </div>

        <!-- Basic Info -->
        <div class="preview-section">
            <div class="section-header">
                <div class="section-title">基本信息</div>
                <div class="section-edit">编辑</div>
            </div>

            <div class="section-content">
                <div class="info-item">
                    <div class="info-label">营业时间</div>
                    <div class="info-value">09:00-21:00</div>
                </div>
                <div class="info-item">
                    <div class="info-label">联系电话</div>
                    <div class="info-value">138****5678</div>
                </div>
                <div class="info-item">
                    <div class="info-label">店铺简介</div>
                    <div class="info-value">本店主营正宗川菜，菜品种类丰富，口味正宗，环境舒适，欢迎品尝。</div>
                </div>
            </div>
        </div>

        <!-- Service Features -->
        <div class="preview-section">
            <div class="section-header">
                <div class="section-title">服务特色</div>
                <div class="section-edit">编辑</div>
            </div>

            <div class="section-content">
                <div class="grid grid-cols-3 gap-2">
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-1">
                            <i class="fas fa-wifi text-green-500"></i>
                        </div>
                        <span class="text-xs">免费WiFi</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-1">
                            <i class="fas fa-calendar-check text-blue-500"></i>
                        </div>
                        <span class="text-xs">支持预订</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mb-1">
                            <i class="fas fa-motorcycle text-yellow-500"></i>
                        </div>
                        <span class="text-xs">支持外卖</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Products -->
        <div class="preview-section">
            <div class="section-header">
                <div class="section-title">热门商品</div>
                <div class="section-edit">编辑</div>
            </div>

            <div class="section-content">
                <div class="product-grid">
                    <div class="product-item">
                        <div class="product-img" style="background-image: url('https://images.unsplash.com/photo-1562967914-608f82629710?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60')"></div>
                        <div class="product-info">
                            <div class="product-name">红烧牛肉面</div>
                            <div class="product-price">¥38</div>
                        </div>
                    </div>
                    <div class="product-item">
                        <div class="product-img" style="background-image: url('https://images.unsplash.com/photo-1563245372-f21724e3856d?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60')"></div>
                        <div class="product-info">
                            <div class="product-name">宫保鸡丁</div>
                            <div class="product-price">¥42</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
        <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
            确认并提交审核
        </button>
    </div>
</body>
</html>
