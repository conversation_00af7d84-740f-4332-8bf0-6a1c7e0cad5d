<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行业特性配置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
        }
        .status-bar::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 24px;
            background-color: #000;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .progress-bar {
            height: 4px;
            background-color: #e5e5e5;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #07c160;
            width: 40%;
        }
        .config-section {
            background-color: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .section-title {
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 10px;
        }
        .form-label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            color: #333;
        }
        .form-input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 13px;
            background-color: #fff;
        }
        .form-select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 13px;
            background-color: #fff;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 10px center;
        }
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            background-color: #f3f4f6;
            border-radius: 16px;
            font-size: 11px;
        }
        .tag.selected {
            background-color: #dcfce7;
            color: #16a34a;
        }
        .tag-close {
            margin-left: 4px;
            font-size: 9px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 22px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #07c160;
        }
        input:checked + .slider:before {
            transform: translateX(18px);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex">
            <i class="fas fa-signal mr-1"></i>
            <i class="fas fa-wifi mr-1"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="bg-white py-2 px-4 flex items-center">
        <div class="w-8">
            <i class="fas fa-arrow-left text-gray-600"></i>
        </div>
        <div class="text-center flex-1">
            <span class="font-medium text-lg">餐饮特性配置</span>
        </div>
        <div class="w-8"></div>
    </div>

    <!-- Progress Bar -->
    <div class="px-4 py-3 bg-white">
        <div class="flex justify-between text-xs text-gray-500 mb-2">
            <span>入驻进度</span>
            <span>4/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-4" style="height: calc(100vh - 150px);">
        <p class="text-gray-600 mb-3 text-sm">请完善您的餐饮店铺特性配置，以便为顾客提供更好的服务体验</p>

        <div class="config-section">
            <h3 class="section-title">基本特性</h3>

            <div class="form-group">
                <label class="form-label">餐厅类型</label>
                <select class="form-select">
                    <option value="" disabled>请选择餐厅类型</option>
                    <option value="1" selected>中餐</option>
                    <option value="2">西餐</option>
                    <option value="3">日韩料理</option>
                    <option value="4">快餐</option>
                    <option value="5">火锅</option>
                    <option value="6">烧烤</option>
                    <option value="7">其他</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">人均消费</label>
                <select class="form-select">
                    <option value="" disabled>请选择人均消费范围</option>
                    <option value="1">50元以下</option>
                    <option value="2" selected>50-100元</option>
                    <option value="3">100-200元</option>
                    <option value="4">200-300元</option>
                    <option value="5">300元以上</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">菜系标签</label>
                <div class="tag-container">
                    <div class="tag selected">川菜 <i class="fas fa-times tag-close"></i></div>
                    <div class="tag selected">家常菜 <i class="fas fa-times tag-close"></i></div>
                    <div class="tag">粤菜</div>
                    <div class="tag">湘菜</div>
                    <div class="tag">鲁菜</div>
                    <div class="tag">东北菜</div>
                    <div class="tag">西北菜</div>
                    <div class="tag">江浙菜</div>
                    <div class="tag">+ 添加</div>
                </div>
            </div>
        </div>

        <div class="config-section">
            <h3 class="section-title">服务特性</h3>

            <div class="flex justify-between items-center mb-4">
                <div>
                    <div class="font-medium">提供WiFi</div>
                    <div class="text-xs text-gray-500">店内提供免费WiFi</div>
                </div>
                <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="flex justify-between items-center mb-4">
                <div>
                    <div class="font-medium">提供停车位</div>
                    <div class="text-xs text-gray-500">店铺附近有停车位</div>
                </div>
                <label class="switch">
                    <input type="checkbox">
                    <span class="slider"></span>
                </label>
            </div>

            <div class="flex justify-between items-center mb-4">
                <div>
                    <div class="font-medium">支持预订</div>
                    <div class="text-xs text-gray-500">支持提前预订座位</div>
                </div>
                <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="flex justify-between items-center mb-4">
                <div>
                    <div class="font-medium">支持外卖</div>
                    <div class="text-xs text-gray-500">提供外卖配送服务</div>
                </div>
                <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="flex justify-between items-center">
                <div>
                    <div class="font-medium">支持自取</div>
                    <div class="text-xs text-gray-500">顾客可到店自取</div>
                </div>
                <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                </label>
            </div>
        </div>

        <div class="config-section">
            <h3 class="section-title">环境设施</h3>

            <div class="form-group">
                <label class="form-label">就餐环境</label>
                <div class="tag-container">
                    <div class="tag selected">大厅</div>
                    <div class="tag selected">包间</div>
                    <div class="tag">露台</div>
                    <div class="tag">吧台</div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">座位数量</label>
                <input type="number" class="form-input" placeholder="请输入座位数量" value="50">
            </div>

            <div class="form-group">
                <label class="form-label">店铺环境照片</label>
                <div class="grid grid-cols-3 gap-2">
                    <div class="h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-plus text-gray-400"></i>
                    </div>
                    <div class="h-16 bg-cover bg-center rounded-lg" style="background-image: url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60')"></div>
                    <div class="h-16 bg-cover bg-center rounded-lg" style="background-image: url('https://images.unsplash.com/photo-1552566626-52f8b828add9?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60')"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
        <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
            保存并继续
        </button>
    </div>
</body>
</html>
