<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开业准备</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
        }
        .status-bar::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 24px;
            background-color: #000;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .progress-bar {
            height: 4px;
            background-color: #e5e5e5;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #07c160;
            width: 100%;
        }
        .prep-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            margin-bottom: 16px;
        }
        .checklist-item {
            display: flex;
            align-items: flex-start;
            padding: 10px 12px;
            border-bottom: 1px solid #f3f4f6;
        }
        .checklist-item:last-child {
            border-bottom: none;
        }
        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            margin-right: 10px;
            flex-shrink: 0;
            position: relative;
        }
        .checkbox.checked {
            background-color: #07c160;
            border-color: #07c160;
        }
        .checkbox.checked::after {
            content: "✓";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
        }
        .checklist-content {
            flex: 1;
        }
        .checklist-title {
            font-weight: 500;
            margin-bottom: 2px;
            font-size: 13px;
        }
        .checklist-desc {
            font-size: 11px;
            color: #6b7280;
        }
        .date-picker {
            display: flex;
            overflow-x: auto;
            padding: 8px 0;
            gap: 8px;
        }
        .date-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 40px;
        }
        .date-day {
            font-size: 10px;
            color: #6b7280;
            margin-bottom: 4px;
        }
        .date-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
        }
        .date-number.selected {
            background-color: #07c160;
            color: white;
        }
        .date-number.today {
            border: 2px solid #07c160;
            color: #07c160;
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            color: #333;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
        }
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        .marketing-option {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        .marketing-option.selected {
            border-color: #07c160;
            background-color: #f0fdf4;
        }
        .marketing-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 16px;
        }
        .marketing-info {
            flex: 1;
        }
        .marketing-title {
            font-weight: 500;
            margin-bottom: 1px;
            font-size: 13px;
        }
        .marketing-desc {
            font-size: 11px;
            color: #6b7280;
        }
        .marketing-price {
            font-weight: 500;
            color: #ef4444;
        }
        .success-banner {
            background: linear-gradient(135deg, #07c160, #10b981);
            border-radius: 12px;
            padding: 15px;
            color: white;
            text-align: center;
            margin-bottom: 15px;
        }
        .success-icon {
            width: 50px;
            height: 50px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex">
            <i class="fas fa-signal mr-1"></i>
            <i class="fas fa-wifi mr-1"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="bg-white py-2 px-4 flex items-center">
        <div class="w-8">
            <i class="fas fa-arrow-left text-gray-600"></i>
        </div>
        <div class="text-center flex-1">
            <span class="font-medium text-lg">开业准备</span>
        </div>
        <div class="w-8"></div>
    </div>

    <!-- Progress Bar -->
    <div class="px-4 py-3 bg-white">
        <div class="flex justify-between text-xs text-gray-500 mb-2">
            <span>入驻进度</span>
            <span>10/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-4" style="height: calc(100vh - 150px);">
        <div class="success-banner">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            <h3 class="text-lg font-bold mb-1">恭喜您完成入驻流程</h3>
            <p class="text-sm">您的店铺即将上线，请完成以下开业准备</p>
        </div>

        <div class="prep-card">
            <h3 class="font-medium p-3 border-b border-gray-100 text-sm">开业前检查清单</h3>

            <div class="checklist-item">
                <div class="checkbox checked"></div>
                <div class="checklist-content">
                    <div class="checklist-title">完善店铺信息</div>
                    <div class="checklist-desc">已完成基本信息填写和资质上传</div>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checkbox checked"></div>
                <div class="checklist-content">
                    <div class="checklist-title">上传商品</div>
                    <div class="checklist-desc">已上传3个商品，建议继续添加更多商品</div>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checkbox"></div>
                <div class="checklist-content">
                    <div class="checklist-title">设置营业时间</div>
                    <div class="checklist-desc">设置每日营业时间和特殊节假日安排</div>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checkbox"></div>
                <div class="checklist-content">
                    <div class="checklist-title">安装收银设备</div>
                    <div class="checklist-desc">安装并测试收银设备和打印机</div>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checkbox"></div>
                <div class="checklist-content">
                    <div class="checklist-title">培训店员</div>
                    <div class="checklist-desc">培训店员使用平台接单和处理订单</div>
                </div>
            </div>
        </div>

        <div class="prep-card p-3">
            <h3 class="font-medium mb-3 text-sm">选择开业日期</h3>

            <div class="date-picker">
                <div class="date-item">
                    <div class="date-day">今天</div>
                    <div class="date-number today">15</div>
                </div>
                <div class="date-item">
                    <div class="date-day">周五</div>
                    <div class="date-number">16</div>
                </div>
                <div class="date-item">
                    <div class="date-day">周六</div>
                    <div class="date-number selected">17</div>
                </div>
                <div class="date-item">
                    <div class="date-day">周日</div>
                    <div class="date-number">18</div>
                </div>
                <div class="date-item">
                    <div class="date-day">周一</div>
                    <div class="date-number">19</div>
                </div>
                <div class="date-item">
                    <div class="date-day">周二</div>
                    <div class="date-number">20</div>
                </div>
                <div class="date-item">
                    <div class="date-day">周三</div>
                    <div class="date-number">21</div>
                </div>
            </div>

            <div class="form-group mt-2">
                <label class="form-label text-xs">开业时间</label>
                <select class="form-select text-sm py-2">
                    <option value="1" selected>09:00</option>
                    <option value="2">10:00</option>
                    <option value="3">11:00</option>
                    <option value="4">12:00</option>
                </select>
            </div>
        </div>

        <div class="prep-card p-3">
            <h3 class="font-medium mb-3 text-sm">开业营销方案</h3>

            <div class="marketing-option selected">
                <div class="marketing-icon bg-yellow-100 text-yellow-500">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="marketing-info">
                    <div class="marketing-title">新店特惠</div>
                    <div class="marketing-desc">新店开业期间，全场商品8.8折</div>
                </div>
                <div class="marketing-price">¥0</div>
            </div>

            <div class="marketing-option">
                <div class="marketing-icon bg-blue-100 text-blue-500">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <div class="marketing-info">
                    <div class="marketing-title">首页推荐</div>
                    <div class="marketing-desc">获得平台首页推荐位展示7天</div>
                </div>
                <div class="marketing-price">¥199</div>
            </div>

            <div class="marketing-option">
                <div class="marketing-icon bg-purple-100 text-purple-500">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="marketing-info">
                    <div class="marketing-title">优惠券包</div>
                    <div class="marketing-desc">发放100张新店体验优惠券</div>
                </div>
                <div class="marketing-price">¥99</div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
        <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
            确认开业
        </button>
    </div>
</body>
</html>
