<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-pending { background: #fef3c7; color: #d97706; }
        .status-confirmed { background: #dbeafe; color: #2563eb; }
        .status-preparing { background: #fde68a; color: #d97706; }
        .status-delivering { background: #c7d2fe; color: #4338ca; }
        .status-completed { background: #d1fae5; color: #059669; }
        .status-cancelled { background: #fee2e2; color: #dc2626; }
        .action-btn {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .search-box {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            transition: all 0.2s;
        }
        .search-box:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .order-timeline {
            position: relative;
            padding-left: 20px;
        }
        .order-timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 12px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 4px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 搜索和筛选区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="flex flex-wrap gap-4 items-center justify-between">
                <div class="flex gap-4 items-center flex-1">
                    <div class="relative flex-1 max-w-md">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input type="text" placeholder="搜索订单号、商家、用户..."
                               class="search-box w-full pl-12 outline-none">
                    </div>
                    <select class="search-box outline-none">
                        <option>全部状态</option>
                        <option>待确认</option>
                        <option>已确认</option>
                        <option>制作中</option>
                        <option>配送中</option>
                        <option>已完成</option>
                        <option>已取消</option>
                    </select>
                    <input type="date" class="search-box outline-none">
                    <select class="search-box outline-none">
                        <option>全部商家</option>
                        <option>麻辣香锅店</option>
                        <option>新鲜水果店</option>
                        <option>便民超市</option>
                    </select>
                </div>
                <button class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                    <i class="fas fa-download mr-2"></i>导出数据
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-5 gap-6 mb-6">
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">今日订单</p>
                        <p class="text-2xl font-bold text-gray-900">2,847</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-lg">
                        <i class="fas fa-shopping-cart text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">待处理</p>
                        <p class="text-2xl font-bold text-orange-600">156</p>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-lg">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">配送中</p>
                        <p class="text-2xl font-bold text-purple-600">89</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-lg">
                        <i class="fas fa-motorcycle text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">已完成</p>
                        <p class="text-2xl font-bold text-green-600">2,456</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-lg">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">今日收益</p>
                        <p class="text-2xl font-bold text-red-600">¥45,680</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-lg">
                        <i class="fas fa-yen-sign text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">订单列表</h3>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单信息</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商家</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">配送信息</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">#ORD20240220001</div>
                                    <div class="text-sm text-gray-500">2024-02-20 14:30</div>
                                    <div class="text-xs text-gray-400">麻辣香锅×2, 米饭×1</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-utensils text-orange-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">麻辣香锅店</div>
                                        <div class="text-sm text-gray-500">张老板</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">李同学</div>
                                    <div class="text-sm text-gray-500">138****5678</div>
                                    <div class="text-xs text-gray-400">东区宿舍楼A座301</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm text-gray-900">骑手: 王师傅</div>
                                    <div class="text-sm text-gray-500">预计: 15:00</div>
                                    <div class="order-timeline mt-2">
                                        <div class="timeline-item">
                                            <div class="text-xs text-gray-600">14:30 订单确认</div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="text-xs text-gray-600">14:35 开始制作</div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="text-xs text-blue-600">14:45 配送中</div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">¥58.00</div>
                                    <div class="text-xs text-gray-500">配送费: ¥3.00</div>
                                    <div class="text-xs text-gray-500">平台抽成: ¥8.70</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-badge status-delivering">配送中</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex gap-2">
                                    <button class="action-btn btn-primary">详情</button>
                                    <button class="action-btn btn-warning">联系</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">#ORD20240220002</div>
                                    <div class="text-sm text-gray-500">2024-02-20 14:25</div>
                                    <div class="text-xs text-gray-400">苹果×3斤, 香蕉×2斤</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-apple-alt text-green-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">新鲜水果店</div>
                                        <div class="text-sm text-gray-500">李经理</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">张同学</div>
                                    <div class="text-sm text-gray-500">139****1234</div>
                                    <div class="text-xs text-gray-400">西区宿舍楼B座205</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm text-orange-600">待分配骑手</div>
                                    <div class="text-sm text-gray-500">预计: 15:10</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">¥42.00</div>
                                    <div class="text-xs text-gray-500">配送费: ¥3.00</div>
                                    <div class="text-xs text-gray-500">平台抽成: ¥6.30</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-badge status-confirmed">已确认</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex gap-2">
                                    <button class="action-btn btn-success">分配</button>
                                    <button class="action-btn btn-primary">详情</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">#ORD20240220003</div>
                                    <div class="text-sm text-gray-500">2024-02-20 14:20</div>
                                    <div class="text-xs text-gray-400">方便面×5, 饮料×3</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-shopping-basket text-purple-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">便民超市</div>
                                        <div class="text-sm text-gray-500">王店长</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">刘同学</div>
                                    <div class="text-sm text-gray-500">137****9876</div>
                                    <div class="text-xs text-gray-400">南区宿舍楼C座108</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm text-gray-900">骑手: 赵师傅</div>
                                    <div class="text-sm text-green-600">已送达</div>
                                    <div class="text-xs text-gray-500">送达时间: 14:55</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">¥35.50</div>
                                    <div class="text-xs text-gray-500">配送费: ¥2.50</div>
                                    <div class="text-xs text-gray-500">平台抽成: ¥5.33</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="status-badge status-completed">已完成</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex gap-2">
                                    <button class="action-btn btn-primary">详情</button>
                                    <button class="action-btn btn-success">评价</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    显示 1-10 条，共 2,847 条记录
                </div>
                <div class="flex gap-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 bg-blue-500 text-white rounded-md text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>


