/* 全局样式 */
:root {
  /* 品牌主色调 */
  --primary-color: #FF4D4F;
  --secondary-color: #FFA39E;
  --text-primary: #333333;
  --text-secondary: #666666;
  --background-color: #F5F5F5;
  
  /* 字体定义 */
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.5;
}

/* 移动端适配 */
@media screen and (max-width: 390px) {
  .container {
    width: 100%;
    padding: 0 15px;
  }
}

/* iOS状态栏 */
.ios-status-bar {
  height: 44px;
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

/* 底部导航栏 */
.tab-bar {
  height: 49px;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #E8E8E8;
}

/* 视频卡片样式 */
.video-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.video-card__header {
  padding: 12px;
  display: flex;
  align-items: center;
}

.video-card__avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
}

.video-card__info {
  flex: 1;
}

.video-card__username {
  font-weight: 600;
  font-size: 16px;
  color: var(--text-primary);
}

.video-card__time {
  font-size: 12px;
  color: var(--text-secondary);
}

.video-card__content {
  width: 100%;
  aspect-ratio: 9/16;
  background: #000;
}

.video-card__actions {
  padding: 12px;
  display: flex;
  justify-content: space-around;
  border-top: 1px solid #F0F0F0;
}

.video-card__action {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  font-size: 14px;
}

.video-card__action-icon {
  margin-right: 4px;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.loading__spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 评论区样式 */
.comments-section {
  background: #fff;
  padding: 16px;
}

.comment-item {
  display: flex;
  margin-bottom: 16px;
}

.comment-item__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 12px;
}

.comment-item__content {
  flex: 1;
}

.comment-item__username {
  font-weight: 500;
  margin-bottom: 4px;
}

.comment-item__text {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 评论面板样式 */
.comments-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80vh;
    background: #fff;
    border-radius: 16px 16px 0 0;
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
}

.comments-panel--active {
    transform: translateY(0);
}

.comments-header {
    padding: 16px;
    border-bottom: 1px solid #E8E8E8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comments-header h3 {
    font-size: 16px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    padding: 4px 8px;
}

.comments-list {
    height: calc(100% - 120px);
    overflow-y: auto;
    padding: 16px;
}

.comment-item {
    display: flex;
    margin-bottom: 20px;
    animation: commentSlideIn 0.3s ease-out;
}

.comment-item__avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 12px;
}

.comment-item__content {
    flex: 1;
}

.comment-item__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.comment-item__username {
    font-weight: 500;
    font-size: 14px;
}

.comment-item__time {
    font-size: 12px;
    color: var(--text-secondary);
}

.comment-item__text {
    font-size: 14px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.comment-item__actions {
    display: flex;
    gap: 16px;
}

.comment-action {
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
}

.comment-input-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background: #fff;
    border-top: 1px solid #E8E8E8;
    display: flex;
    gap: 12px;
    align-items: center;
}

.comment-input {
    flex: 1;
    border: none;
    background: #F5F5F5;
    padding: 10px 16px;
    border-radius: 20px;
    font-size: 14px;
}

.send-btn {
    background: var(--primary-color);
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
}

.send-btn:disabled {
    background: #ccc;
}

@keyframes commentSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 评论遮罩层 */
.comments-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 999;
}

.comments-overlay--active {
    opacity: 1;
    pointer-events: auto;
}

/* 动效相关 */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}

.slide-up {
  transform: translateY(100%);
  transition: transform 300ms ease-out;
}

.slide-up-active {
  transform: translateY(0);
}

/* 按钮反馈 */
.btn-hover {
  opacity: 0.8;
  transition: opacity 150ms ease;
}

/* 双击点赞动画 */
.big-heart-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-color);
    font-size: 0;
    opacity: 0;
    animation: heartBeat 1s ease-in-out;
    pointer-events: none;
}

@keyframes heartBeat {
    0% {
        font-size: 0;
        opacity: 0;
    }
    50% {
        font-size: 100px;
        opacity: 1;
    }
    100% {
        font-size: 120px;
        opacity: 0;
    }
}

/* 点赞按钮动画 */
.like-button--active {
    animation: likeButtonPop 0.3s ease;
}

@keyframes likeButtonPop {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* 视频加载动画 */
.video-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.video-loading .loading__spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
