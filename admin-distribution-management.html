<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分销管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .distribution-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 24px;
            transition: all 0.3s ease;
            border-left: 4px solid #3b82f6;
        }
        .distribution-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-active { background: #d1fae5; color: #059669; }
        .status-inactive { background: #fee2e2; color: #dc2626; }
        .status-pending { background: #fef3c7; color: #d97706; }
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { transform: translateY(-1px); }
        .form-input, .form-select {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.2s;
            background: #fafbfc;
        }
        .form-input:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
            background: white;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
        }
        .area-tag {
            display: inline-block;
            padding: 4px 8px;
            background: #f1f5f9;
            color: #475569;
            border-radius: 6px;
            font-size: 12px;
            margin: 2px;
        }
        .performance-bar {
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }
        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题和操作 -->
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">分销管理</h1>
                <p class="text-gray-500 mt-1">管理分销公司信息、区域分配和业绩统计</p>
            </div>
            <button class="btn btn-success">
                <i class="fas fa-plus mr-2"></i>添加分销公司
            </button>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-4 gap-6 mb-6">
            <div class="metric-card">
                <div class="text-3xl font-bold mb-2">8</div>
                <div class="text-sm opacity-90">分销公司总数</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold mb-2">156</div>
                <div class="text-sm opacity-90">活跃骑手数</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold mb-2">2,847</div>
                <div class="text-sm opacity-90">今日配送订单</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold mb-2">89.2%</div>
                <div class="text-sm opacity-90">平均成功率</div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="flex gap-4 items-center">
                <select class="form-select">
                    <option>全部状态</option>
                    <option>正常运营</option>
                    <option>暂停服务</option>
                    <option>待审核</option>
                </select>
                <select class="form-select">
                    <option>全部区域</option>
                    <option>东区</option>
                    <option>西区</option>
                    <option>南区</option>
                    <option>北区</option>
                </select>
                <input type="text" placeholder="搜索公司名称..." class="form-input flex-1">
                <button class="btn btn-primary">
                    <i class="fas fa-search mr-2"></i>搜索
                </button>
            </div>
        </div>

        <!-- 分销公司列表 -->
        <div class="space-y-6">
            <!-- 分销公司1 -->
            <div class="distribution-card">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-truck text-blue-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">校园配送有限公司</h3>
                            <p class="text-gray-500">成立时间: 2020-03-15 | 合作时长: 4年</p>
                            <div class="flex items-center mt-2">
                                <span class="status-badge status-active">正常运营</span>
                                <span class="ml-4 text-sm text-gray-500">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i>4.8分
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <button class="btn btn-primary">详情</button>
                        <button class="btn btn-warning">编辑</button>
                        <button class="btn btn-danger">暂停</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-4 gap-6 mb-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">45</div>
                        <div class="text-sm text-gray-500">骑手数量</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">1,456</div>
                        <div class="text-sm text-gray-500">今日订单</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">92.3%</div>
                        <div class="text-sm text-gray-500">成功率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">¥21,840</div>
                        <div class="text-sm text-gray-500">今日收益</div>
                    </div>
                </div>
                
                <div class="border-t pt-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-semibold text-gray-700">负责人信息</span>
                        <span class="font-semibold text-gray-700">服务区域</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-900">张经理 | 138****5678</p>
                            <p class="text-sm text-gray-500"><EMAIL></p>
                        </div>
                        <div class="text-right">
                            <div class="area-tag">东区宿舍楼</div>
                            <div class="area-tag">南区宿舍楼</div>
                            <div class="area-tag">教学区</div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>本月业绩完成度</span>
                            <span>92.3%</span>
                        </div>
                        <div class="performance-bar">
                            <div class="performance-fill" style="width: 92.3%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分销公司2 -->
            <div class="distribution-card">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-motorcycle text-green-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">快递小哥配送</h3>
                            <p class="text-gray-500">成立时间: 2019-08-20 | 合作时长: 5年</p>
                            <div class="flex items-center mt-2">
                                <span class="status-badge status-active">正常运营</span>
                                <span class="ml-4 text-sm text-gray-500">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i>4.6分
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <button class="btn btn-primary">详情</button>
                        <button class="btn btn-warning">编辑</button>
                        <button class="btn btn-danger">暂停</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-4 gap-6 mb-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">38</div>
                        <div class="text-sm text-gray-500">骑手数量</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">1,391</div>
                        <div class="text-sm text-gray-500">今日订单</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">88.7%</div>
                        <div class="text-sm text-gray-500">成功率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">¥19,560</div>
                        <div class="text-sm text-gray-500">今日收益</div>
                    </div>
                </div>
                
                <div class="border-t pt-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-semibold text-gray-700">负责人信息</span>
                        <span class="font-semibold text-gray-700">服务区域</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-900">李主管 | 139****1234</p>
                            <p class="text-sm text-gray-500"><EMAIL></p>
                        </div>
                        <div class="text-right">
                            <div class="area-tag">西区宿舍楼</div>
                            <div class="area-tag">北区宿舍楼</div>
                            <div class="area-tag">体育馆区</div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>本月业绩完成度</span>
                            <span>88.7%</span>
                        </div>
                        <div class="performance-bar">
                            <div class="performance-fill" style="width: 88.7%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分销公司3 -->
            <div class="distribution-card">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-shipping-fast text-orange-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">闪送校园</h3>
                            <p class="text-gray-500">成立时间: 2022-01-10 | 合作时长: 2年</p>
                            <div class="flex items-center mt-2">
                                <span class="status-badge status-pending">待审核</span>
                                <span class="ml-4 text-sm text-gray-500">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i>4.2分
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <button class="btn btn-success">审核</button>
                        <button class="btn btn-primary">详情</button>
                        <button class="btn btn-danger">拒绝</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-4 gap-6 mb-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">25</div>
                        <div class="text-sm text-gray-500">骑手数量</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">856</div>
                        <div class="text-sm text-gray-500">今日订单</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">85.4%</div>
                        <div class="text-sm text-gray-500">成功率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">¥12,840</div>
                        <div class="text-sm text-gray-500">今日收益</div>
                    </div>
                </div>
                
                <div class="border-t pt-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-semibold text-gray-700">负责人信息</span>
                        <span class="font-semibold text-gray-700">申请服务区域</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-900">王总监 | 137****9876</p>
                            <p class="text-sm text-gray-500"><EMAIL></p>
                        </div>
                        <div class="text-right">
                            <div class="area-tag">全校区</div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>试运营业绩</span>
                            <span>85.4%</span>
                        </div>
                        <div class="performance-bar">
                            <div class="performance-fill" style="width: 85.4%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="mt-6 flex justify-between items-center">
            <div class="text-sm text-gray-500">
                显示 1-3 条，共 8 条记录
            </div>
            <div class="flex gap-2">
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">上一页</button>
                <button class="px-3 py-1 bg-blue-500 text-white rounded-md text-sm">1</button>
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">2</button>
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">3</button>
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">下一页</button>
            </div>
        </div>
    </div>
</body>
</html>
