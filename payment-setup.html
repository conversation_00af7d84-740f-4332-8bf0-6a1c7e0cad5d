<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结算设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f7f7f7;
            height: 100vh;
            overflow: hidden;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
        }
        .status-bar::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 24px;
            background-color: #000;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .progress-bar {
            height: 4px;
            background-color: #e5e5e5;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #07c160;
            width: 90%;
        }
        .payment-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            margin-bottom: 16px;
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            color: #333;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
        }
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        .bank-card {
            background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
            border-radius: 12px;
            padding: 12px;
            color: white;
            margin-bottom: 12px;
        }
        .bank-logo {
            width: 50px;
            height: 30px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
        }
        .card-number {
            font-size: 16px;
            letter-spacing: 2px;
            margin-bottom: 12px;
        }
        .card-info {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }
        .payment-method {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        .payment-method.selected {
            border-color: #07c160;
            background-color: #f0fdf4;
        }
        .payment-icon {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        .payment-info {
            flex: 1;
        }
        .payment-radio {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #d1d5db;
            position: relative;
        }
        .payment-radio.selected {
            border-color: #07c160;
        }
        .payment-radio.selected::after {
            content: "";
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #07c160;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex">
            <i class="fas fa-signal mr-1"></i>
            <i class="fas fa-wifi mr-1"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Navigation Bar -->
    <div class="bg-white py-2 px-4 flex items-center">
        <div class="w-8">
            <i class="fas fa-arrow-left text-gray-600"></i>
        </div>
        <div class="text-center flex-1">
            <span class="font-medium text-lg">结算设置</span>
        </div>
        <div class="w-8"></div>
    </div>

    <!-- Progress Bar -->
    <div class="px-4 py-3 bg-white">
        <div class="flex justify-between text-xs text-gray-500 mb-2">
            <span>入驻进度</span>
            <span>9/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-4" style="height: calc(100vh - 150px);">
        <p class="text-gray-600 mb-3 text-xs">请设置您的结算账户和收款方式</p>

        <div class="payment-card p-3 mb-4">
            <h3 class="font-medium mb-3 text-sm">结算账户</h3>

            <div class="bank-card">
                <div class="bank-logo">
                    <i class="fas fa-university text-white text-xl"></i>
                </div>
                <div class="card-number">**** **** **** 5678</div>
                <div class="card-info">
                    <div>张三</div>
                    <div>中国建设银行</div>
                </div>
            </div>

            <button class="w-full py-2 text-center text-blue-500 text-sm">
                更换结算账户
            </button>
        </div>

        <div class="payment-card p-3 mb-4">
            <h3 class="font-medium mb-3 text-sm">结算周期</h3>

            <div class="form-group">
                <label class="form-label">选择结算周期</label>
                <select class="form-select">
                    <option value="1" selected>T+1（次日结算）</option>
                    <option value="2">T+3（3天结算）</option>
                    <option value="3">T+7（7天结算）</option>
                    <option value="4">月结（每月15日结算上月订单）</option>
                </select>
            </div>

            <p class="text-xs text-gray-500">
                <i class="fas fa-info-circle mr-1"></i>
                结算周期是指从订单完成到资金到账的时间间隔
            </p>
        </div>

        <div class="payment-card p-3">
            <h3 class="font-medium mb-3 text-sm">收款方式</h3>

            <div class="payment-method selected">
                <div class="payment-icon bg-green-100">
                    <i class="fab fa-weixin text-green-500"></i>
                </div>
                <div class="payment-info">
                    <div class="font-medium">微信支付</div>
                    <div class="text-xs text-gray-500">支持微信扫码、小程序支付</div>
                </div>
                <div class="payment-radio selected"></div>
            </div>

            <div class="payment-method">
                <div class="payment-icon bg-blue-100">
                    <i class="fab fa-alipay text-blue-500"></i>
                </div>
                <div class="payment-info">
                    <div class="font-medium">支付宝</div>
                    <div class="text-xs text-gray-500">支持支付宝扫码支付</div>
                </div>
                <div class="payment-radio"></div>
            </div>

            <div class="payment-method">
                <div class="payment-icon bg-yellow-100">
                    <i class="fas fa-credit-card text-yellow-500"></i>
                </div>
                <div class="payment-info">
                    <div class="font-medium">银行卡支付</div>
                    <div class="text-xs text-gray-500">支持各大银行借记卡、信用卡</div>
                </div>
                <div class="payment-radio"></div>
            </div>

            <div class="payment-method">
                <div class="payment-icon bg-purple-100">
                    <i class="fas fa-money-bill-wave text-purple-500"></i>
                </div>
                <div class="payment-info">
                    <div class="font-medium">现金支付</div>
                    <div class="text-xs text-gray-500">仅适用于到店消费</div>
                </div>
                <div class="payment-radio"></div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
        <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
            保存并继续
        </button>
    </div>
</body>
</html>
