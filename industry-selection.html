<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行业选择 - 商家入驻</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f6f6f6;
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        /* iOS Status Bar */
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }

        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }

        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }

        .wechat-nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #07C160;
        }

        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Progress Bar */
        .progress-container {
            padding: 12px 16px;
            background-color: #ffffff;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }

        .progress-bar {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background-color: #07C160;
            width: 10%;
        }

        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px - 57px);
            padding: 16px;
            background-color: #f6f6f6;
            display: flex;
            flex-direction: column;
        }

        .content-description {
            font-size: 13px;
            color: #666;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        /* Industry Grid */
        .industry-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            flex: 1;
        }

        .industry-card {
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }

        .industry-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .industry-icon {
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .industry-icon i {
            font-size: 30px;
        }

        .industry-info {
            padding: 8px;
            border-top: 1px solid #f5f5f5;
        }

        .industry-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .industry-desc {
            font-size: 11px;
            color: #999;
        }

        /* Bottom Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }

        .tab-item.active {
            color: #07C160;
        }

        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }

        /* Bottom Button */
        .bottom-button {
            padding: 12px 16px;
            background-color: #ffffff;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 50px;
            width: 100%;
            box-sizing: border-box;
        }

        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: 100%;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="wechat-nav-title">选择行业</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-info">
            <span>入驻进度</span>
            <span>1/10</span>
        </div>
        <div class="progress-bar">
            <div class="progress"></div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <p class="content-description">请选择您的商家类型，我们将为您提供相应的入驻流程和资质要求</p>

        <div class="industry-grid">
            <!-- Restaurant -->
            <div class="industry-card">
                <div class="industry-icon" style="background-color: #FFF7E6;">
                    <i class="fas fa-utensils" style="color: #FA8C16;"></i>
                </div>
                <div class="industry-info">
                    <h3 class="industry-title">餐饮美食</h3>
                    <p class="industry-desc">餐厅、小吃、咖啡厅等</p>
                </div>
            </div>

            <!-- Beauty -->
            <div class="industry-card">
                <div class="industry-icon" style="background-color: #FFF0F6;">
                    <i class="fas fa-spa" style="color: #EB2F96;"></i>
                </div>
                <div class="industry-info">
                    <h3 class="industry-title">美容美发</h3>
                    <p class="industry-desc">美发、美甲、SPA等</p>
                </div>
            </div>

            <!-- Entertainment -->
            <div class="industry-card">
                <div class="industry-icon" style="background-color: #F9F0FF;">
                    <i class="fas fa-music" style="color: #722ED1;"></i>
                </div>
                <div class="industry-info">
                    <h3 class="industry-title">休闲娱乐</h3>
                    <p class="industry-desc">KTV、电影院、游戏厅等</p>
                </div>
            </div>

            <!-- Retail -->
            <div class="industry-card">
                <div class="industry-icon" style="background-color: #E6F7FF;">
                    <i class="fas fa-shopping-bag" style="color: #1890FF;"></i>
                </div>
                <div class="industry-info">
                    <h3 class="industry-title">零售商店</h3>
                    <p class="industry-desc">服装、电子、杂货等</p>
                </div>
            </div>

            <!-- Fresh Food -->
            <div class="industry-card">
                <div class="industry-icon" style="background-color: #E6FFFB;">
                    <i class="fas fa-fish" style="color: #13C2C2;"></i>
                </div>
                <div class="industry-info">
                    <h3 class="industry-title">生鲜食品</h3>
                    <p class="industry-desc">水产、肉类、蔬果等</p>
                </div>
            </div>

            <!-- Hotel -->
            <div class="industry-card">
                <div class="industry-icon" style="background-color: #F0F5FF;">
                    <i class="fas fa-hotel" style="color: #2F54EB;"></i>
                </div>
                <div class="industry-info">
                    <h3 class="industry-title">酒店住宿</h3>
                    <p class="industry-desc">酒店、民宿、公寓等</p>
                </div>
            </div>

            <!-- Education -->
            <div class="industry-card">
                <div class="industry-icon" style="background-color: #FCFFE6;">
                    <i class="fas fa-graduation-cap" style="color: #A0D911;"></i>
                </div>
                <div class="industry-info">
                    <h3 class="industry-title">教育培训</h3>
                    <p class="industry-desc">培训机构、辅导班等</p>
                </div>
            </div>

            <!-- More -->
            <div class="industry-card">
                <div class="industry-icon" style="background-color: #F5F5F5;">
                    <i class="fas fa-ellipsis-h" style="color: #8C8C8C;"></i>
                </div>
                <div class="industry-info">
                    <h3 class="industry-title">更多行业</h3>
                    <p class="industry-desc">查看全部行业类型</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Button -->
    <div class="bottom-button">
        <button class="btn-primary">下一步</button>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
