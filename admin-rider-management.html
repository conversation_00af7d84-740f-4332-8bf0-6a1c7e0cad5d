<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>骑手管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .rider-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .rider-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-online { background: #d1fae5; color: #059669; }
        .status-offline { background: #fee2e2; color: #dc2626; }
        .status-busy { background: #fef3c7; color: #d97706; }
        .status-break { background: #e0e7ff; color: #3730a3; }
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { transform: translateY(-1px); }
        .form-input, .form-select {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.2s;
            background: #fafbfc;
        }
        .form-input:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
            background: white;
        }
        .metric-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }
        .rating-stars {
            color: #fbbf24;
        }
        .performance-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .perf-excellent { background: #10b981; }
        .perf-good { background: #3b82f6; }
        .perf-average { background: #f59e0b; }
        .perf-poor { background: #ef4444; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">骑手管理</h1>
                <p class="text-gray-500 mt-1">管理配送骑手信息、状态和业绩</p>
            </div>
            <button class="btn btn-success">
                <i class="fas fa-plus mr-2"></i>添加骑手
            </button>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-5 gap-6 mb-6">
            <div class="metric-card">
                <div class="text-3xl font-bold text-gray-900 mb-2">156</div>
                <div class="text-sm text-gray-500">总骑手数</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-green-600 mb-2">89</div>
                <div class="text-sm text-gray-500">在线骑手</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-orange-600 mb-2">45</div>
                <div class="text-sm text-gray-500">配送中</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-blue-600 mb-2">32</div>
                <div class="text-sm text-gray-500">休息中</div>
            </div>
            <div class="metric-card">
                <div class="text-3xl font-bold text-red-600 mb-2">22</div>
                <div class="text-sm text-gray-500">离线</div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="flex gap-4 items-center">
                <select class="form-select">
                    <option>全部状态</option>
                    <option>在线</option>
                    <option>配送中</option>
                    <option>休息中</option>
                    <option>离线</option>
                </select>
                <select class="form-select">
                    <option>全部分销公司</option>
                    <option>校园配送有限公司</option>
                    <option>快递小哥配送</option>
                    <option>闪送校园</option>
                </select>
                <select class="form-select">
                    <option>全部区域</option>
                    <option>东区</option>
                    <option>西区</option>
                    <option>南区</option>
                    <option>北区</option>
                </select>
                <input type="text" placeholder="搜索骑手姓名或手机号..." class="form-input flex-1">
                <button class="btn btn-primary">
                    <i class="fas fa-search mr-2"></i>搜索
                </button>
            </div>
        </div>

        <!-- 骑手列表 -->
        <div class="space-y-4">
            <!-- 骑手1 -->
            <div class="rider-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-blue-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-900">王师傅</h3>
                            <p class="text-sm text-gray-500">ID: R001 | 手机: 138****5678</p>
                            <p class="text-sm text-gray-500">所属: 校园配送有限公司 | 入职: 2023-03-15</p>
                            <div class="flex items-center mt-2">
                                <span class="status-badge status-online">在线</span>
                                <span class="ml-4 text-sm">
                                    <span class="performance-indicator perf-excellent"></span>
                                    优秀骑手
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-gray-900">156</div>
                                <div class="text-xs text-gray-500">今日订单</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-green-600">98.5%</div>
                                <div class="text-xs text-gray-500">成功率</div>
                            </div>
                            <div class="text-center">
                                <div class="flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-900 mr-1">4.9</span>
                                    <div class="rating-stars">
                                        <i class="fas fa-star text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">用户评分</div>
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <button class="btn btn-primary text-xs">详情</button>
                            <button class="btn btn-warning text-xs">消息</button>
                            <button class="btn btn-danger text-xs">暂停</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 骑手2 -->
            <div class="rider-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-green-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-900">李师傅</h3>
                            <p class="text-sm text-gray-500">ID: R002 | 手机: 139****1234</p>
                            <p class="text-sm text-gray-500">所属: 快递小哥配送 | 入职: 2023-01-20</p>
                            <div class="flex items-center mt-2">
                                <span class="status-badge status-busy">配送中</span>
                                <span class="ml-4 text-sm">
                                    <span class="performance-indicator perf-good"></span>
                                    良好骑手
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-gray-900">128</div>
                                <div class="text-xs text-gray-500">今日订单</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-green-600">95.2%</div>
                                <div class="text-xs text-gray-500">成功率</div>
                            </div>
                            <div class="text-center">
                                <div class="flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-900 mr-1">4.6</span>
                                    <div class="rating-stars">
                                        <i class="fas fa-star text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">用户评分</div>
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <button class="btn btn-primary text-xs">详情</button>
                            <button class="btn btn-warning text-xs">消息</button>
                            <button class="btn btn-success text-xs">定位</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 骑手3 -->
            <div class="rider-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-purple-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-900">张师傅</h3>
                            <p class="text-sm text-gray-500">ID: R003 | 手机: 137****9876</p>
                            <p class="text-sm text-gray-500">所属: 校园配送有限公司 | 入职: 2023-06-10</p>
                            <div class="flex items-center mt-2">
                                <span class="status-badge status-break">休息中</span>
                                <span class="ml-4 text-sm">
                                    <span class="performance-indicator perf-average"></span>
                                    普通骑手
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-gray-900">89</div>
                                <div class="text-xs text-gray-500">今日订单</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-orange-600">88.7%</div>
                                <div class="text-xs text-gray-500">成功率</div>
                            </div>
                            <div class="text-center">
                                <div class="flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-900 mr-1">4.2</span>
                                    <div class="rating-stars">
                                        <i class="fas fa-star text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">用户评分</div>
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <button class="btn btn-primary text-xs">详情</button>
                            <button class="btn btn-warning text-xs">消息</button>
                            <button class="btn btn-success text-xs">呼叫</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 骑手4 -->
            <div class="rider-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-red-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-900">赵师傅</h3>
                            <p class="text-sm text-gray-500">ID: R004 | 手机: 135****4321</p>
                            <p class="text-sm text-gray-500">所属: 闪送校园 | 入职: 2024-01-15</p>
                            <div class="flex items-center mt-2">
                                <span class="status-badge status-offline">离线</span>
                                <span class="ml-4 text-sm">
                                    <span class="performance-indicator perf-poor"></span>
                                    待改进
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-gray-900">45</div>
                                <div class="text-xs text-gray-500">今日订单</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-red-600">76.3%</div>
                                <div class="text-xs text-gray-500">成功率</div>
                            </div>
                            <div class="text-center">
                                <div class="flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-900 mr-1">3.8</span>
                                    <div class="rating-stars">
                                        <i class="fas fa-star text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">用户评分</div>
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <button class="btn btn-primary text-xs">详情</button>
                            <button class="btn btn-warning text-xs">培训</button>
                            <button class="btn btn-danger text-xs">警告</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 骑手5 -->
            <div class="rider-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-indigo-600 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-900">刘师傅</h3>
                            <p class="text-sm text-gray-500">ID: R005 | 手机: 136****7890</p>
                            <p class="text-sm text-gray-500">所属: 快递小哥配送 | 入职: 2023-09-05</p>
                            <div class="flex items-center mt-2">
                                <span class="status-badge status-online">在线</span>
                                <span class="ml-4 text-sm">
                                    <span class="performance-indicator perf-good"></span>
                                    良好骑手
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-gray-900">112</div>
                                <div class="text-xs text-gray-500">今日订单</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-green-600">92.8%</div>
                                <div class="text-xs text-gray-500">成功率</div>
                            </div>
                            <div class="text-center">
                                <div class="flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-900 mr-1">4.5</span>
                                    <div class="rating-stars">
                                        <i class="fas fa-star text-xs"></i>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">用户评分</div>
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <button class="btn btn-primary text-xs">详情</button>
                            <button class="btn btn-warning text-xs">消息</button>
                            <button class="btn btn-success text-xs">分配</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="mt-6 flex justify-between items-center">
            <div class="text-sm text-gray-500">
                显示 1-5 条，共 156 条记录
            </div>
            <div class="flex gap-2">
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">上一页</button>
                <button class="px-3 py-1 bg-blue-500 text-white rounded-md text-sm">1</button>
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">2</button>
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">3</button>
                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">下一页</button>
            </div>
        </div>
    </div>
</body>
</html>
