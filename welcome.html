<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎页 - 商家入驻</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f6f6f6;
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        /* iOS Status Bar */
        .ios-status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
            z-index: 10;
        }

        .status-icons {
            display: flex;
            align-items: center;
        }

        .status-icons i {
            margin-left: 5px;
        }

        /* WeChat Mini Program Navigation */
        .wechat-nav {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            border-bottom: 1px solid #f0f0f0;
        }

        .wechat-nav-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }

        .wechat-nav-menu {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Content Area */
        .content-area {
            height: calc(100vh - 88px - 50px);
            background-color: #ffffff;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* Bottom Tab Bar */
        .tab-bar {
            height: 50px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            width: 25%;
        }

        .tab-item.active {
            color: #07C160;
        }

        .tab-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }

        .welcome-header {
            padding: 20px 20px;
            text-align: center;
            background-image: url('https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            position: relative;
        }

        .welcome-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .welcome-content {
            position: relative;
            z-index: 1;
        }

        .welcome-logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            background-color: #07C160;
            border-radius: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 30px;
        }

        .welcome-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .welcome-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .features-section {
            padding: 15px 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .feature-icon {
            width: 36px;
            height: 36px;
            background-color: #E8F5E9;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #07C160;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .feature-content {
            flex: 1;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #333;
        }

        .feature-desc {
            font-size: 12px;
            color: #999;
            line-height: 1.4;
        }

        .btn-primary {
            background-color: #07C160;
            color: white;
            border-radius: 4px;
            padding: 10px 16px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            width: calc(100% - 40px);
            text-align: center;
            margin: 0 auto 10px;
            display: block;
        }

        .login-link {
            text-align: center;
            font-size: 14px;
            color: #999;
            margin-bottom: 10px;
        }

        .login-link a {
            color: #07C160;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <div>9:41</div>
        <div class="status-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- WeChat Mini Program Navigation -->
    <div class="wechat-nav">
        <div class="wechat-nav-title">商家入驻</div>
        <div class="wechat-nav-menu">
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
        <div class="welcome-header">
            <div class="welcome-content">
                <div class="welcome-logo">
                    <i class="fas fa-store"></i>
                </div>
                <h1 class="welcome-title">欢迎入驻本地生活平台</h1>
                <p class="welcome-subtitle">一站式商家入驻解决方案，助力您的业务快速上线</p>
            </div>
        </div>

        <div class="features-section">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">快速入驻</h3>
                    <p class="feature-desc">简化入驻流程，最快30分钟完成全部资料提交，3个工作日内审核完成</p>
                </div>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">海量用户</h3>
                    <p class="feature-desc">接入平台后，立即获得数百万活跃用户的曝光机会，提升店铺流量</p>
                </div>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">增加收入</h3>
                    <p class="feature-desc">平均提升30%以上的营业额，多元化的营销工具助力业绩增长</p>
                </div>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">安全保障</h3>
                    <p class="feature-desc">严格的资质审核和平台监管，为商家和用户提供安全可靠的交易环境</p>
                </div>
            </div>
        </div>

        <button class="btn-primary">立即开始入驻</button>

        <div class="login-link">
            已有账号？<a href="#">立即登录</a>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-list"></i>
            <span>进度</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-file-alt"></i>
            <span>资料</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
