<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家审核</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 220px;
            background-color: #001529;
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 32px;
            height: 32px;
            background-color: #1890ff;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 16px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .menu-item.active {
            color: white;
            background-color: #1890ff;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .header-trigger {
            font-size: 18px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .header-trigger:hover {
            color: #1890ff;
        }
        
        .header-breadcrumb {
            margin-left: 24px;
            display: flex;
            align-items: center;
        }
        
        .breadcrumb-item {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item:not(:last-child)::after {
            content: "/";
            margin: 0 8px;
            color: #d9d9d9;
        }
        
        .breadcrumb-item:last-child {
            color: #333;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .header-icon:hover {
            color: #1890ff;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .verification-tabs {
            display: flex;
            background-color: white;
            border-radius: 4px;
            margin-bottom: 24px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .tab-item {
            flex: 1;
            padding: 16px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 2px solid transparent;
        }
        
        .tab-item:hover {
            color: #1890ff;
        }
        
        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }
        
        .tab-count {
            display: inline-block;
            margin-left: 8px;
            padding: 2px 8px;
            background-color: #f5f5f5;
            border-radius: 10px;
            font-size: 12px;
            color: #666;
        }
        
        .verification-card {
            background-color: white;
            border-radius: 4px;
            margin-bottom: 24px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .verification-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .verification-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .verification-content {
            padding: 24px;
        }
        
        .verification-steps {
            display: flex;
            margin-bottom: 24px;
        }
        
        .step-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }
        
        .step-item:not(:last-child)::after {
            content: "";
            position: absolute;
            top: 16px;
            right: -50%;
            width: 100%;
            height: 2px;
            background-color: #f0f0f0;
            z-index: 1;
        }
        
        .step-item.active:not(:last-child)::after {
            background-color: #1890ff;
        }
        
        .step-item.completed:not(:last-child)::after {
            background-color: #52c41a;
        }
        
        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #f0f0f0;
            color: #999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
        }
        
        .step-item.active .step-icon {
            background-color: #1890ff;
            color: white;
        }
        
        .step-item.completed .step-icon {
            background-color: #52c41a;
            color: white;
        }
        
        .step-title {
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        
        .step-item.active .step-title {
            color: #1890ff;
            font-weight: 500;
        }
        
        .step-item.completed .step-title {
            color: #52c41a;
        }
        
        .verification-form {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .form-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        
        .form-select:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
            resize: vertical;
            min-height: 80px;
        }
        
        .form-textarea:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .document-preview {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-top: 16px;
        }
        
        .document-item {
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .document-image {
            width: 100%;
            height: 120px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 32px;
        }
        
        .document-info {
            padding: 12px;
            border-top: 1px solid #f0f0f0;
        }
        
        .document-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .document-meta {
            font-size: 12px;
            color: #999;
        }
        
        .verification-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 24px;
            gap: 12px;
        }
        
        .action-button {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .action-button i {
            margin-right: 8px;
        }
        
        .action-default {
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .action-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .action-primary {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        
        .action-primary:hover {
            background-color: #40a9ff;
        }
        
        .action-danger {
            background-color: white;
            color: #ff4d4f;
            border: 1px solid #ff4d4f;
        }
        
        .action-danger:hover {
            background-color: #ff4d4f;
            color: white;
        }
        
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }
        
        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-store"></i>
                </div>
                <div class="sidebar-title">商家入驻系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-user-check menu-icon"></i>
                    <span>审核管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span>统计报表</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-user menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统设置</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shield-alt menu-icon"></i>
                    <span>权限管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-file-alt menu-icon"></i>
                    <span>日志管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="header-trigger">
                        <i class="fas fa-bars"></i>
                    </div>
                    <div class="header-breadcrumb">
                        <span class="breadcrumb-item">审核管理</span>
                        <span class="breadcrumb-item">商家审核</span>
                        <span class="breadcrumb-item">品味小厨</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">5</div>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">商家审核 - 品味小厨</h1>
                </div>
                
                <div class="verification-tabs">
                    <div class="tab-item active">待审核 <span class="tab-count">42</span></div>
                    <div class="tab-item">已通过 <span class="tab-count">1,208</span></div>
                    <div class="tab-item">已拒绝 <span class="tab-count">36</span></div>
                    <div class="tab-item">需补充资料 <span class="tab-count">18</span></div>
                </div>
                
                <div class="verification-card">
                    <div class="verification-header">
                        <div class="verification-title">审核进度</div>
                    </div>
                    <div class="verification-content">
                        <div class="verification-steps">
                            <div class="step-item completed">
                                <div class="step-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="step-title">基本信息</div>
                            </div>
                            <div class="step-item active">
                                <div class="step-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="step-title">资质审核</div>
                            </div>
                            <div class="step-item">
                                <div class="step-icon">
                                    <i class="fas fa-shopping-bag"></i>
                                </div>
                                <div class="step-title">商品审核</div>
                            </div>
                            <div class="step-item">
                                <div class="step-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="step-title">审核完成</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="verification-card">
                    <div class="verification-header">
                        <div class="verification-title">基本信息</div>
                    </div>
                    <div class="verification-content">
                        <div class="verification-form">
                            <div class="form-group">
                                <div class="form-label">商家名称</div>
                                <div class="form-value">品味小厨</div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">商家类型</div>
                                <div class="form-value">餐饮 - 中式快餐</div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">联系人</div>
                                <div class="form-value">张先生</div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">联系电话</div>
                                <div class="form-value">13812345678</div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">营业地址</div>
                                <div class="form-value">北京市朝阳区建国路88号</div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">营业时间</div>
                                <div class="form-value">周一至周日 10:00-22:00</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="verification-card">
                    <div class="verification-header">
                        <div class="verification-title">资质证明</div>
                    </div>
                    <div class="verification-content">
                        <div class="document-preview">
                            <div class="document-item">
                                <div class="document-image">
                                    <i class="fas fa-file-image"></i>
                                </div>
                                <div class="document-info">
                                    <div class="document-name">营业执照</div>
                                    <div class="document-meta">上传时间: 2023-05-18 14:10</div>
                                </div>
                            </div>
                            <div class="document-item">
                                <div class="document-image">
                                    <i class="fas fa-file-image"></i>
                                </div>
                                <div class="document-info">
                                    <div class="document-name">食品经营许可证</div>
                                    <div class="document-meta">上传时间: 2023-05-18 14:12</div>
                                </div>
                            </div>
                            <div class="document-item">
                                <div class="document-image">
                                    <i class="fas fa-file-image"></i>
                                </div>
                                <div class="document-info">
                                    <div class="document-name">法人身份证正面</div>
                                    <div class="document-meta">上传时间: 2023-05-18 14:15</div>
                                </div>
                            </div>
                            <div class="document-item">
                                <div class="document-image">
                                    <i class="fas fa-file-image"></i>
                                </div>
                                <div class="document-info">
                                    <div class="document-name">法人身份证反面</div>
                                    <div class="document-meta">上传时间: 2023-05-18 14:15</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-6 mt-6">
                            <div class="form-group">
                                <div class="form-label">资质审核结果</div>
                                <select class="form-select">
                                    <option value="1">通过</option>
                                    <option value="2">不通过</option>
                                    <option value="3">需补充资料</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-label">审核意见</div>
                                <textarea class="form-textarea" placeholder="请输入审核意见"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="verification-card">
                    <div class="verification-header">
                        <div class="verification-title">商品信息</div>
                    </div>
                    <div class="verification-content">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>商品名称</th>
                                    <th>分类</th>
                                    <th>价格</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>宫保鸡丁</td>
                                    <td>热菜</td>
                                    <td>¥38</td>
                                    <td><span class="status-tag status-pending">待审核</span></td>
                                    <td><a href="#" style="color: #1890ff;">查看</a></td>
                                </tr>
                                <tr>
                                    <td>水煮鱼</td>
                                    <td>热菜</td>
                                    <td>¥58</td>
                                    <td><span class="status-tag status-pending">待审核</span></td>
                                    <td><a href="#" style="color: #1890ff;">查看</a></td>
                                </tr>
                                <tr>
                                    <td>麻婆豆腐</td>
                                    <td>热菜</td>
                                    <td>¥28</td>
                                    <td><span class="status-tag status-pending">待审核</span></td>
                                    <td><a href="#" style="color: #1890ff;">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div class="grid grid-cols-2 gap-6 mt-6">
                            <div class="form-group">
                                <div class="form-label">商品审核结果</div>
                                <select class="form-select">
                                    <option value="1">全部通过</option>
                                    <option value="2">部分通过</option>
                                    <option value="3">全部不通过</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-label">审核意见</div>
                                <textarea class="form-textarea" placeholder="请输入审核意见"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="verification-actions">
                    <button class="action-button action-default">
                        <i class="fas fa-arrow-left"></i>
                        <span>返回列表</span>
                    </button>
                    <button class="action-button action-danger">
                        <i class="fas fa-times"></i>
                        <span>拒绝申请</span>
                    </button>
                    <button class="action-button action-primary">
                        <i class="fas fa-check"></i>
                        <span>审核通过</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
