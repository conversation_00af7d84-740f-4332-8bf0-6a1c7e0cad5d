<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家入驻系统原型</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            padding: 20px;
        }
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(375px, 1fr));
            gap: 50px;
            padding: 20px;
        }
        .phone-frame {
            width: 375px;
            background: white;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            position: relative;
            margin: 0 auto;
            border: 10px solid #111;
            padding-bottom: 20px;
        }
        .screen-title {
            text-align: center;
            font-weight: bold;
            margin-top: 10px;
            margin-bottom: 15px;
            font-size: 18px;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #000;
            position: relative;
        }
        .status-bar::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 24px;
            background-color: #000;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .progress-bar {
            height: 4px;
            background-color: #e5e5e5;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #07c160;
        }
        .bottom-button {
            background-color: white;
            border-top: 1px solid #e5e5e5;
            padding: 16px;
            margin-top: 20px;
        }
        /* 通用样式 */
        .section-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            margin-bottom: 16px;
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            color: #333;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
        }
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            font-size: 14px;
            background-color: #fff;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        .tag {
            display: inline-block;
            padding: 4px 8px;
            background-color: #f3f4f6;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1 class="text-3xl font-bold text-center mb-8">商家入驻系统原型图</h1>

    <div class="prototype-container">
        <!-- 欢迎页 -->
        <div>
            <div class="screen-title">欢迎页</div>
            <div class="phone-frame">
                <!-- Status Bar -->
                <div class="status-bar">
                    <div>9:41</div>
                    <div class="flex">
                        <i class="fas fa-signal mr-1"></i>
                        <i class="fas fa-wifi mr-1"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>

                <!-- Navigation Bar -->
                <div class="bg-white py-2 px-4 flex items-center">
                    <div class="text-center flex-1">
                        <span class="font-medium text-lg">商家入驻</span>
                    </div>
                </div>

                <!-- Main Content -->
                <div style="height: 200px; background-image: url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80'); background-size: cover; background-position: center;"></div>

                <div class="p-6 bg-white">
                    <h1 class="text-2xl font-bold mb-4">欢迎加入我们</h1>
                    <p class="text-gray-600 mb-6">成为我们的合作商家，让您的业务获得更多曝光和客户</p>

                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-4">
                                <i class="fas fa-store text-green-500"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold">扩大业务</h3>
                                <p class="text-sm text-gray-500">接触更多潜在客户</p>
                            </div>
                        </div>

                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                <i class="fas fa-chart-line text-blue-500"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold">增加收入</h3>
                                <p class="text-sm text-gray-500">提高销售额和利润</p>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-4">
                                <i class="fas fa-tools text-purple-500"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold">专业工具</h3>
                                <p class="text-sm text-gray-500">使用我们的平台管理您的业务</p>
                            </div>
                        </div>
                    </div>

                    <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
                        立即开始入驻
                    </button>

                    <p class="text-center text-sm text-gray-500 mt-4">
                        已有账号？ <a href="#" class="text-green-500">登录</a>
                    </p>
                </div>

                <!-- Tab Bar -->
                <div class="bg-white py-3 border-t border-gray-200">
                    <div class="flex justify-around">
                        <div class="flex flex-col items-center text-green-500">
                            <i class="fas fa-home text-xl mb-1"></i>
                            <span class="text-xs">首页</span>
                        </div>
                        <div class="flex flex-col items-center text-gray-400">
                            <i class="fas fa-compass text-xl mb-1"></i>
                            <span class="text-xs">发现</span>
                        </div>
                        <div class="flex flex-col items-center text-gray-400">
                            <i class="fas fa-bell text-xl mb-1"></i>
                            <span class="text-xs">消息</span>
                        </div>
                        <div class="flex flex-col items-center text-gray-400">
                            <i class="fas fa-user text-xl mb-1"></i>
                            <span class="text-xs">我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 行业选择 -->
        <div>
            <div class="screen-title">行业选择</div>
            <div class="phone-frame">
                <!-- Status Bar -->
                <div class="status-bar">
                    <div>9:41</div>
                    <div class="flex">
                        <i class="fas fa-signal mr-1"></i>
                        <i class="fas fa-wifi mr-1"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>

                <!-- Navigation Bar -->
                <div class="bg-white py-2 px-4 flex items-center">
                    <div class="w-8">
                        <i class="fas fa-arrow-left text-gray-600"></i>
                    </div>
                    <div class="text-center flex-1">
                        <span class="font-medium text-lg">选择行业</span>
                    </div>
                    <div class="w-8"></div>
                </div>

                <!-- Progress Bar -->
                <div class="px-4 py-3 bg-white">
                    <div class="flex justify-between text-xs text-gray-500 mb-2">
                        <span>入驻进度</span>
                        <span>1/10</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress" style="width: 10%"></div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="p-4 bg-gray-50">
                    <p class="text-gray-600 mb-4">请选择您的商家类型，我们将为您提供相应的入驻流程</p>

                    <div class="grid grid-cols-2 gap-4">
                        <!-- Restaurant -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="h-24 bg-yellow-50 flex items-center justify-center">
                                <i class="fas fa-utensils text-4xl text-yellow-500"></i>
                            </div>
                            <div class="p-3">
                                <h3 class="font-medium">餐饮美食</h3>
                                <p class="text-xs text-gray-500">餐厅、小吃、咖啡厅等</p>
                            </div>
                        </div>

                        <!-- Beauty -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="h-24 bg-pink-50 flex items-center justify-center">
                                <i class="fas fa-spa text-4xl text-pink-500"></i>
                            </div>
                            <div class="p-3">
                                <h3 class="font-medium">美容美发</h3>
                                <p class="text-xs text-gray-500">美发、美甲、SPA等</p>
                            </div>
                        </div>

                        <!-- Entertainment -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="h-24 bg-purple-50 flex items-center justify-center">
                                <i class="fas fa-music text-4xl text-purple-500"></i>
                            </div>
                            <div class="p-3">
                                <h3 class="font-medium">休闲娱乐</h3>
                                <p class="text-xs text-gray-500">KTV、电影院、游戏厅等</p>
                            </div>
                        </div>

                        <!-- Retail -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="h-24 bg-blue-50 flex items-center justify-center">
                                <i class="fas fa-shopping-bag text-4xl text-blue-500"></i>
                            </div>
                            <div class="p-3">
                                <h3 class="font-medium">零售商店</h3>
                                <p class="text-xs text-gray-500">服装、电子、杂货等</p>
                            </div>
                        </div>

                        <!-- Fresh Food -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="h-24 bg-green-50 flex items-center justify-center">
                                <i class="fas fa-fish text-4xl text-green-500"></i>
                            </div>
                            <div class="p-3">
                                <h3 class="font-medium">生鲜食品</h3>
                                <p class="text-xs text-gray-500">水产、肉类、蔬果等</p>
                            </div>
                        </div>

                        <!-- Hotel -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="h-24 bg-indigo-50 flex items-center justify-center">
                                <i class="fas fa-hotel text-4xl text-indigo-500"></i>
                            </div>
                            <div class="p-3">
                                <h3 class="font-medium">酒店住宿</h3>
                                <p class="text-xs text-gray-500">酒店、民宿、公寓等</p>
                            </div>
                        </div>

                        <!-- More -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="h-24 bg-gray-50 flex items-center justify-center">
                                <i class="fas fa-ellipsis-h text-4xl text-gray-500"></i>
                            </div>
                            <div class="p-3">
                                <h3 class="font-medium">更多行业</h3>
                                <p class="text-xs text-gray-500">查看全部行业类型</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bottom Button -->
                <div class="bottom-button">
                    <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
                        下一步
                    </button>
                </div>
            </div>
        </div>

        <!-- 基础信息 -->
        <div>
            <div class="screen-title">基础信息</div>
            <div class="phone-frame">
                <!-- Status Bar -->
                <div class="status-bar">
                    <div>9:41</div>
                    <div class="flex">
                        <i class="fas fa-signal mr-1"></i>
                        <i class="fas fa-wifi mr-1"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>

                <!-- Navigation Bar -->
                <div class="bg-white py-2 px-4 flex items-center">
                    <div class="w-8">
                        <i class="fas fa-arrow-left text-gray-600"></i>
                    </div>
                    <div class="text-center flex-1">
                        <span class="font-medium text-lg">基础信息</span>
                    </div>
                    <div class="w-8"></div>
                </div>

                <!-- Progress Bar -->
                <div class="px-4 py-3 bg-white">
                    <div class="flex justify-between text-xs text-gray-500 mb-2">
                        <span>入驻进度</span>
                        <span>2/10</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress" style="width: 20%"></div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="p-4 bg-gray-50">
                    <p class="text-gray-600 mb-4">请填写您的商家基本信息，带<span class="text-red-500">*</span>为必填项</p>

                    <form>
                        <div class="form-group">
                            <label class="form-label">商家名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="form-input" placeholder="请输入商家名称">
                        </div>

                        <div class="form-group">
                            <label class="form-label">联系人 <span class="text-red-500">*</span></label>
                            <input type="text" class="form-input" placeholder="请输入联系人姓名">
                        </div>

                        <div class="form-group">
                            <label class="form-label">联系电话 <span class="text-red-500">*</span></label>
                            <input type="tel" class="form-input" placeholder="请输入联系电话">
                        </div>

                        <div class="form-group">
                            <label class="form-label">电子邮箱</label>
                            <input type="email" class="form-input" placeholder="请输入电子邮箱">
                        </div>

                        <div class="form-group">
                            <label class="form-label">经营类型 <span class="text-red-500">*</span></label>
                            <select class="form-select">
                                <option value="" disabled selected>请选择经营类型</option>
                                <option value="1">个体工商户</option>
                                <option value="2">有限责任公司</option>
                                <option value="3">股份有限公司</option>
                                <option value="4">其他</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">营业时间 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-2">
                                <input type="text" class="form-input" placeholder="开始时间" value="09:00">
                                <span class="flex items-center">至</span>
                                <input type="text" class="form-input" placeholder="结束时间" value="21:00">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">店铺地址 <span class="text-red-500">*</span></label>
                            <div style="height: 150px; background-image: url('https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*SYcTR5JK5GQAAAAAAAAAAAAADrJ8AQ/original'); background-size: cover; background-position: center; border-radius: 8px; position: relative; margin-bottom: 8px;">
                                <i class="fas fa-map-marker-alt text-red-500 text-2xl absolute" style="top: 50%; left: 50%; transform: translate(-50%, -100%);"></i>
                            </div>
                            <input type="text" class="form-input" placeholder="请输入详细地址">
                        </div>

                        <div class="form-group">
                            <label class="form-label">店铺简介</label>
                            <textarea class="form-input" rows="3" placeholder="请简要描述您的店铺特色和服务"></textarea>
                        </div>
                    </form>
                </div>

                <!-- Bottom Button -->
                <div class="bottom-button">
                    <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
                        保存并继续
                    </button>
                </div>
            </div>
        </div>

        <!-- 资质上传 -->
        <div>
            <div class="screen-title">资质上传</div>
            <div class="phone-frame">
                <!-- Status Bar -->
                <div class="status-bar">
                    <div>9:41</div>
                    <div class="flex">
                        <i class="fas fa-signal mr-1"></i>
                        <i class="fas fa-wifi mr-1"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>

                <!-- Navigation Bar -->
                <div class="bg-white py-2 px-4 flex items-center">
                    <div class="w-8">
                        <i class="fas fa-arrow-left text-gray-600"></i>
                    </div>
                    <div class="text-center flex-1">
                        <span class="font-medium text-lg">资质上传</span>
                    </div>
                    <div class="w-8"></div>
                </div>

                <!-- Progress Bar -->
                <div class="px-4 py-3 bg-white">
                    <div class="flex justify-between text-xs text-gray-500 mb-2">
                        <span>入驻进度</span>
                        <span>3/10</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress" style="width: 30%"></div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="p-4 bg-gray-50">
                    <p class="text-gray-600 mb-4">请上传您的营业资质证明，确保图片清晰可见</p>

                    <div class="bg-white rounded-xl p-4 mb-4">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <div class="font-medium">营业执照</div>
                                <div class="text-xs text-gray-500">请上传营业执照正本或副本</div>
                            </div>
                            <div class="px-2 py-1 bg-red-100 text-red-500 text-xs rounded">必传</div>
                        </div>

                        <div class="border border-dashed border-gray-300 rounded-lg h-32 flex flex-col items-center justify-center bg-gray-50">
                            <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-500">点击上传图片</p>
                            <p class="text-xs text-gray-400 mt-1">支持JPG、PNG格式，大小不超过5MB</p>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 mb-4">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <div class="font-medium">食品经营许可证</div>
                                <div class="text-xs text-gray-500">适用于餐饮类商家</div>
                            </div>
                            <div class="px-2 py-1 bg-red-100 text-red-500 text-xs rounded">必传</div>
                        </div>

                        <div class="mb-2">
                            <img src="https://images.unsplash.com/photo-1568219656418-15c329312bf1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" class="w-full h-32 object-cover rounded-lg">
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">food_license.jpg</span>
                            <div class="flex space-x-2">
                                <button class="text-xs text-blue-500">重新上传</button>
                                <button class="text-xs text-red-500">删除</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 mb-4">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <div class="font-medium">法人身份证</div>
                                <div class="text-xs text-gray-500">请上传法人身份证正反面</div>
                            </div>
                            <div class="px-2 py-1 bg-red-100 text-red-500 text-xs rounded">必传</div>
                        </div>

                        <div class="grid grid-cols-2 gap-3 mb-3">
                            <div class="border border-dashed border-gray-300 rounded-lg h-24 flex flex-col items-center justify-center bg-gray-50">
                                <i class="fas fa-id-card text-gray-400 text-xl"></i>
                                <p class="text-xs text-gray-500 mt-1">身份证正面</p>
                            </div>
                            <div class="border border-dashed border-gray-300 rounded-lg h-24 flex flex-col items-center justify-center bg-gray-50">
                                <i class="fas fa-id-card-alt text-gray-400 text-xl"></i>
                                <p class="text-xs text-gray-500 mt-1">身份证反面</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 mb-4">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <div class="font-medium">卫生许可证</div>
                                <div class="text-xs text-gray-500">适用于餐饮、美容美发等行业</div>
                            </div>
                        </div>

                        <div class="border border-dashed border-gray-300 rounded-lg h-32 flex flex-col items-center justify-center bg-gray-50">
                            <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-500">点击上传图片</p>
                            <p class="text-xs text-gray-400 mt-1">支持JPG、PNG格式，大小不超过5MB</p>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <div class="font-medium">其他资质证明</div>
                                <div class="text-xs text-gray-500">可上传其他相关资质证明</div>
                            </div>
                        </div>

                        <div class="border border-dashed border-gray-300 rounded-lg h-32 flex flex-col items-center justify-center bg-gray-50">
                            <i class="fas fa-plus text-gray-400 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-500">添加更多资质</p>
                        </div>
                    </div>
                </div>

                <!-- Bottom Button -->
                <div class="bottom-button">
                    <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
                        保存并继续
                    </button>
                </div>
            </div>
        </div>

        <!-- 行业特性配置 -->
        <div>
            <div class="screen-title">行业特性配置</div>
            <div class="phone-frame">
                <!-- Status Bar -->
                <div class="status-bar">
                    <div>9:41</div>
                    <div class="flex">
                        <i class="fas fa-signal mr-1"></i>
                        <i class="fas fa-wifi mr-1"></i>
                        <i class="fas fa-battery-full"></i>
                    </div>
                </div>

                <!-- Navigation Bar -->
                <div class="bg-white py-2 px-4 flex items-center">
                    <div class="w-8">
                        <i class="fas fa-arrow-left text-gray-600"></i>
                    </div>
                    <div class="text-center flex-1">
                        <span class="font-medium text-lg">餐饮特性配置</span>
                    </div>
                    <div class="w-8"></div>
                </div>

                <!-- Progress Bar -->
                <div class="px-4 py-3 bg-white">
                    <div class="flex justify-between text-xs text-gray-500 mb-2">
                        <span>入驻进度</span>
                        <span>4/10</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress" style="width: 40%"></div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="p-4 bg-gray-50">
                    <p class="text-gray-600 mb-4">请完善您的餐饮店铺特性配置，以便为顾客提供更好的服务体验</p>

                    <div class="bg-white rounded-xl p-4 mb-4">
                        <h3 class="font-medium mb-4">基本特性</h3>

                        <div class="form-group">
                            <label class="form-label">餐厅类型</label>
                            <select class="form-select">
                                <option value="" disabled>请选择餐厅类型</option>
                                <option value="1" selected>中餐</option>
                                <option value="2">西餐</option>
                                <option value="3">日韩料理</option>
                                <option value="4">快餐</option>
                                <option value="5">火锅</option>
                                <option value="6">烧烤</option>
                                <option value="7">其他</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">人均消费</label>
                            <select class="form-select">
                                <option value="" disabled>请选择人均消费范围</option>
                                <option value="1">50元以下</option>
                                <option value="2" selected>50-100元</option>
                                <option value="3">100-200元</option>
                                <option value="4">200-300元</option>
                                <option value="5">300元以上</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">菜系标签</label>
                            <div class="flex flex-wrap gap-2">
                                <div class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs flex items-center">
                                    川菜 <i class="fas fa-times ml-1"></i>
                                </div>
                                <div class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs flex items-center">
                                    家常菜 <i class="fas fa-times ml-1"></i>
                                </div>
                                <div class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">粤菜</div>
                                <div class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">湘菜</div>
                                <div class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">鲁菜</div>
                                <div class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">东北菜</div>
                                <div class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">+ 添加</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 mb-4">
                        <h3 class="font-medium mb-4">服务特性</h3>

                        <div class="flex justify-between items-center mb-4">
                            <div>
                                <div class="font-medium">提供WiFi</div>
                                <div class="text-xs text-gray-500">店内提供免费WiFi</div>
                            </div>
                            <div class="relative w-11 h-6 bg-green-500 rounded-full">
                                <div class="absolute w-4 h-4 bg-white rounded-full top-1 right-1"></div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center mb-4">
                            <div>
                                <div class="font-medium">提供停车位</div>
                                <div class="text-xs text-gray-500">店铺附近有停车位</div>
                            </div>
                            <div class="relative w-11 h-6 bg-gray-300 rounded-full">
                                <div class="absolute w-4 h-4 bg-white rounded-full top-1 left-1"></div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center mb-4">
                            <div>
                                <div class="font-medium">支持预订</div>
                                <div class="text-xs text-gray-500">支持提前预订座位</div>
                            </div>
                            <div class="relative w-11 h-6 bg-green-500 rounded-full">
                                <div class="absolute w-4 h-4 bg-white rounded-full top-1 right-1"></div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center mb-4">
                            <div>
                                <div class="font-medium">支持外卖</div>
                                <div class="text-xs text-gray-500">提供外卖配送服务</div>
                            </div>
                            <div class="relative w-11 h-6 bg-green-500 rounded-full">
                                <div class="absolute w-4 h-4 bg-white rounded-full top-1 right-1"></div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium">支持自取</div>
                                <div class="text-xs text-gray-500">顾客可到店自取</div>
                            </div>
                            <div class="relative w-11 h-6 bg-green-500 rounded-full">
                                <div class="absolute w-4 h-4 bg-white rounded-full top-1 right-1"></div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4">
                        <h3 class="font-medium mb-4">环境设施</h3>

                        <div class="form-group">
                            <label class="form-label">就餐环境</label>
                            <div class="flex flex-wrap gap-2">
                                <div class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs">大厅</div>
                                <div class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs">包间</div>
                                <div class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">露台</div>
                                <div class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">吧台</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">座位数量</label>
                            <input type="number" class="form-input" placeholder="请输入座位数量" value="50">
                        </div>

                        <div class="form-group">
                            <label class="form-label">店铺环境照片</label>
                            <div class="grid grid-cols-3 gap-2">
                                <div class="h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-plus text-gray-400"></i>
                                </div>
                                <div class="h-20 bg-cover bg-center rounded-lg" style="background-image: url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60')"></div>
                                <div class="h-20 bg-cover bg-center rounded-lg" style="background-image: url('https://images.unsplash.com/photo-1552566626-52f8b828add9?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60')"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bottom Button -->
                <div class="bottom-button">
                    <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium">
                        保存并继续
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
